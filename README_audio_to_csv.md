# Audio to CSV Converter

This tool converts audio files to CSV format with subtitles grouped by time frames. It uses the existing codebase functions for audio transcription and subtitle processing.

## Features

- **Audio Transcription**: Uses Whisper model to transcribe audio to SRT format
- **Time-based Grouping**: Groups subtitles by specified time frames (e.g., 4 seconds)
- **CSV Output**: Generates tab-separated CSV files with Start Time and Subtitle columns
- **Flexible Usage**: Can be used as a command-line tool or imported as a module

## Files

1. **`audio_to_csv_converter.py`** - Command-line script
2. **`audio_to_csv_module.py`** - Module for programmatic use
3. **`README_audio_to_csv.md`** - This documentation

## Requirements

The scripts use functions from your existing codebase:
- `utils.srt_utils.transcribe_audio()` - For audio transcription
- `utils.srt_utils.club_subtitles()` - For grouping subtitles by time frame

Make sure you have the required dependencies installed:
- `whisper`
- `pysrt`
- `csv` (built-in)
- `tempfile` (built-in)

## Usage

### Command Line Usage

```bash
python audio_to_csv_converter.py <audio_file> <time_frame_seconds> [output_csv_file]
```

**Examples:**
```bash
# Convert audio.mp3 with 4-second time frames
python audio_to_csv_converter.py audio.mp3 4

# Specify output file
python audio_to_csv_converter.py audio.mp3 4 output.csv

# Use 2-second time frames
python audio_to_csv_converter.py audio.mp3 2
```

### Programmatic Usage

```python
from audio_to_csv_module import audio_to_csv, get_csv_content

# Convert audio to CSV file
csv_file = audio_to_csv("audio.mp3", 4, "output.csv")
print(f"Generated: {csv_file}")

# Get content without saving to file
content = get_csv_content("audio.mp3", 4)
for start_time, subtitle in content:
    print(f"{start_time}: {subtitle}")
```

## Output Format

The CSV file will have the following format:

```
Start Time	Subtitle
00:00:00,000	You're marrying the man who sent me to prison. Jared's voice trembled.
00:00:04,000	I can't believe you would do this to me after everything we've been through.
00:00:08,000	The betrayal cuts deeper than any knife ever could.
```

### CSV Format Details

- **Delimiter**: Tab (`\t`)
- **Encoding**: UTF-8
- **Start Time Format**: `HH:MM:SS,mmm` (e.g., `00:00:04,000`)
- **Subtitle**: Combined text from all subtitles within the time frame

## How It Works

1. **Audio Transcription**: Uses the existing `transcribe_audio()` function with Whisper model
2. **SRT Generation**: Creates a temporary SRT file with timestamps and text
3. **Time Grouping**: Uses the existing `club_subtitles()` function to group subtitles by time frame
4. **CSV Output**: Formats the grouped data into CSV with proper timestamp formatting

## Time Frame Logic

The `club_subtitles()` function groups subtitles based on time gaps:
- If the gap between consecutive subtitles is less than the specified time frame, they are combined
- If the gap is greater than or equal to the time frame, a new group starts
- Each group contains all subtitles that fall within the time frame

## Example Output

For a 4-second time frame:

**Input Audio**: "You're marrying the man who sent me to prison. Jared's voice trembled."

**Output CSV**:
```
Start Time	Subtitle
00:00:00,000	You're marrying the man who sent me to prison. Jared's voice trembled.
```

## Error Handling

The scripts include comprehensive error handling:
- File existence validation
- Time frame validation (must be positive integer)
- Temporary file cleanup
- Detailed error messages

## Dependencies

The scripts rely on your existing codebase structure:
```
discord/
├── utils/
│   └── srt_utils.py  # Contains transcribe_audio() and club_subtitles()
├── audio_to_csv_converter.py
├── audio_to_csv_module.py
└── README_audio_to_csv.md
```

## Notes

- The scripts use temporary SRT files that are automatically cleaned up
- The output CSV uses tab delimiters for better compatibility with spreadsheet applications
- All timestamps are formatted in SRT format (HH:MM:SS,mmm)
- The scripts maintain the existing codebase's error handling and retry mechanisms 