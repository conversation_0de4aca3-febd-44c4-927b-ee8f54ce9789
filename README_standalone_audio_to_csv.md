# Standalone Audio to CSV Converter

A completely standalone Python tool that converts audio files to CSV format with subtitles grouped by time frames using word-level timestamps from AssemblyAI.

## Features

- **Standalone**: No dependencies on existing codebase
- **Word-level Transcription**: Uses AssemblyAI for precise word-level timestamps
- **Time-based Grouping**: Groups words by specified time frames (e.g., 4 seconds)
- **CSV Output**: Generates tab-separated CSV files with Start Time and Subtitle columns
- **Multiple Usage Options**: Command-line script, module import, or direct function calls

## Files

1. **`audio_to_csv_standalone.py`** - Command-line script
2. **`audio_to_csv_standalone_module.py`** - Module for programmatic use
3. **`README_standalone_audio_to_csv.md`** - This documentation

## Requirements

### Python Dependencies
```bash
pip install requests moviepy
```

### API Key
You need an AssemblyAI API key:
1. Sign up at [AssemblyAI](https://www.assemblyai.com/)
2. Get your API key from the dashboard
3. Set it as an environment variable:
   ```bash
   export ASSEMBLYAI_API_KEY='your_api_key_here'
   ```

## Usage

### Command Line Usage

```bash
python audio_to_csv_standalone.py <audio_file> <time_frame_seconds> [output_csv_file]
```

**Examples:**
```bash
# Convert audio.mp3 with 4-second time frames
python audio_to_csv_standalone.py audio.mp3 4

# Specify output file
python audio_to_csv_standalone.py audio.mp3 4 output.csv

# Use 2-second time frames
python audio_to_csv_standalone.py audio.mp3 2
```

### Programmatic Usage

```python
from audio_to_csv_standalone_module import audio_to_csv, get_csv_content, save_word_timestamps_to_json

# Convert audio to CSV file
csv_file = audio_to_csv("audio.mp3", 4, "output.csv")
print(f"Generated: {csv_file}")

# Get content without saving to file
content = get_csv_content("audio.mp3", 4)
for start_time, subtitle in content:
    print(f"{start_time}: {subtitle}")

# Save word timestamps to JSON (for debugging/analysis)
json_file = save_word_timestamps_to_json("audio.mp3")
print(f"Word timestamps saved to: {json_file}")
```

## Output Format

The CSV file will have the following format:

```
Start Time	Subtitle
00:00:00,000	You're marrying the man who sent me to prison. Jared's voice trembled.
00:00:04,000	I can't believe you would do this to me after everything we've been through.
00:00:08,000	The betrayal cuts deeper than any knife ever could.
```

### CSV Format Details

- **Delimiter**: Tab (`\t`)
- **Encoding**: UTF-8
- **Start Time Format**: `HH:MM:SS,mmm` (e.g., `00:00:04,000`)
- **Subtitle**: Combined text from all words within the time frame

## How It Works

1. **Audio Upload**: Uploads audio file to AssemblyAI
2. **Word-level Transcription**: Gets precise timestamps for each word spoken
3. **Time Grouping**: Groups words based on gaps between them (if gap < time_frame, words are combined)
4. **CSV Output**: Formats grouped data into CSV with proper timestamp formatting

## Time Frame Logic

The grouping algorithm works as follows:
- Words are processed sequentially
- If the gap between consecutive words is less than the specified time frame, they are grouped together
- If the gap is greater than or equal to the time frame, a new group starts
- Each group contains all words that fall within the time frame

## Word Timestamp Structure

The AssemblyAI API returns word-level data in this format:
```json
[
  {
    "text": "You're",
    "start": 0,
    "end": 500
  },
  {
    "text": "marrying",
    "start": 500,
    "end": 1200
  },
  {
    "text": "the",
    "start": 1200,
    "end": 1400
  }
]
```

Where `start` and `end` are in milliseconds.

## Example Output

For your example with 4-second time frames:

**Input Audio**: "You're marrying the man who sent me to prison. Jared's voice trembled."

**Output CSV**:
```
Start Time	Subtitle
00:00:00,000	You're marrying the man who sent me to prison. Jared's voice trembled.
```

## Error Handling

The scripts include comprehensive error handling:
- File existence validation
- API key validation
- Time frame validation (must be positive integer)
- Transcription error handling
- Network timeout handling

## API Key Setup

### Option 1: Environment Variable (Recommended)
```bash
export ASSEMBLYAI_API_KEY='your_api_key_here'
```

### Option 2: Pass as Parameter
```python
csv_file = audio_to_csv("audio.mp3", 4, api_key="your_api_key_here")
```

## Supported Audio Formats

AssemblyAI supports most common audio formats:
- MP3, MP4, M4A, WAV, FLAC, AVI, MOV, and more
- Maximum file size: 1GB
- Maximum duration: 5 hours

## Performance Notes

- Transcription time depends on audio length (typically 1-2x audio duration)
- Word-level timestamps provide precise timing information
- The grouping algorithm is efficient and handles large files well

## Troubleshooting

### Common Issues

1. **API Key Error**: Make sure your AssemblyAI API key is set correctly
2. **File Not Found**: Check that the audio file path is correct
3. **Transcription Timeout**: For very long files, increase the timeout in the code
4. **No Words Found**: Check that the audio file contains speech

### Debug Mode

Use the `verbose=True` parameter to see detailed progress:
```python
csv_file = audio_to_csv("audio.mp3", 4, verbose=True)
```

## Comparison with Codebase Version

| Feature | Standalone | Codebase Version |
|---------|------------|------------------|
| Dependencies | Minimal (requests, moviepy) | Full codebase |
| API Key | AssemblyAI only | Multiple services |
| Word-level | Yes | Yes |
| SRT Generation | No | Yes |
| Error Handling | Basic | Advanced |
| Integration | None | Full pipeline |

## Notes

- The standalone version focuses on the core functionality: audio → word timestamps → grouped CSV
- It's designed to be portable and easy to use in any environment
- The word-level approach provides more precise timing than SRT-based grouping
- All timestamps are in SRT format for compatibility with video editing tools 