#!/usr/bin/env python3
"""
Test script for the parallel replace_pronoun_in_srt function with full SRT file
"""

import os
import sys
import shutil
import re
from datetime import datetime

# Add the project root to Python path
sys.path.append('/Users/<USER>/PycharmProjects/discord')

try:
    from utils.gpt_service import OpenAIService
    from utils.helpers import load_gpt_prompt
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the project directory")
    sys.exit(1)

def parse_srt_simple(content):
    """Simple SRT parser"""
    import re
    
    # Split content into blocks
    blocks = re.split(r'\n\s*\n', content.strip())
    entries = []
    
    for block in blocks:
        if not block.strip():
            continue
            
        lines = block.strip().split('\n')
        if len(lines) < 3:
            continue
            
        try:
            # Parse index
            index = int(lines[0])
            
            # Parse timestamp
            timestamp_line = lines[1]
            if ' --> ' not in timestamp_line:
                continue
                
            start_time, end_time = timestamp_line.split(' --> ')
            
            # Parse text (everything after timestamp)
            text = '\n'.join(lines[2:])
            
            entries.append({
                'index': index,
                'start': start_time.strip(),
                'end': end_time.strip(),
                'text': text.strip()
            })
        except (ValueError, IndexError):
            continue
            
    return entries

def count_pronouns_in_text(text):
    """Count pronouns in text"""
    pronouns = ['he', 'she', 'him', 'her', 'his', 'hers', 'they', 'them', 'their', 'theirs']
    text_lower = text.lower()
    count = 0
    for pronoun in pronouns:
        # Use word boundaries to avoid partial matches
        pattern = r'\b' + re.escape(pronoun) + r'\b'
        count += len(re.findall(pattern, text_lower))
    return count

def test_parallel_full_file():
    """Test the parallel pronoun replacement function with full SRT file"""
    print("🚀 Testing PARALLEL replace_pronoun_in_srt with full SRT file")
    print("=" * 70)
    
    # File paths
    original_file = "test_full_srt.srt"
    backup_file = "test_full_srt_backup.srt"
    output_file = "/Users/<USER>/Downloads/processed_full_output.srt"
    
    if not os.path.exists(original_file):
        print(f"❌ Test file not found: {original_file}")
        return
    
    # Create backup
    shutil.copy2(original_file, backup_file)
    print(f"💾 Created backup: {backup_file}")
    
    try:
        # Analyze original file
        print(f"\n🔍 ORIGINAL FILE ANALYSIS:")
        with open(original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        entries = parse_srt_simple(content)
        total_entries = len(entries)
        total_pronouns = sum(count_pronouns_in_text(entry['text']) for entry in entries)
        
        print(f"📈 Total SRT entries: {total_entries}")
        print(f"👤 Total pronouns found: {total_pronouns}")
        
        # Show first few entries with pronouns
        print(f"\n📝 First 5 entries with pronouns:")
        pronoun_count = 0
        for entry in entries[:30]:  # Check first 30 entries
            if count_pronouns_in_text(entry['text']) > 0:
                pronoun_count += 1
                print(f"  Entry {entry['index']}: {entry['text'][:60]}...")
                if pronoun_count >= 5:
                    break
        
        # Load system prompt - this appears to be a drama story
        print(f"\n📋 Loading system prompt...")
        system_prompt = load_gpt_prompt("replace_pronoun", "drama")
        print(f"✅ System prompt loaded ({len(system_prompt)} characters)")
        
        # Create OpenAI service instance
        service = OpenAIService(task_id="test_parallel_full", story_id="test_story_parallel")
        
        # Run the PARALLEL pronoun replacement
        print(f"\n🚀 Running PARALLEL pronoun replacement...")
        start_time = datetime.now()
        
        try:
            service.replace_pronoun_in_srt(original_file, system_prompt)
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            print(f"✅ PARALLEL processing completed in {processing_time:.2f} seconds")
            
        except Exception as e:
            print(f"❌ Processing failed: {str(e)}")
            # Restore backup
            shutil.copy2(backup_file, original_file)
            print(f"🔄 Restored original file from backup")
            return
        
        # Analyze processed file
        print(f"\n🔍 PROCESSED FILE ANALYSIS:")
        with open(original_file, 'r', encoding='utf-8') as f:
            processed_content = f.read()
        
        processed_entries = parse_srt_simple(processed_content)
        processed_total = len(processed_entries)
        processed_pronouns = sum(count_pronouns_in_text(entry['text']) for entry in processed_entries)
        
        # Validation check
        print(f"\n📊 VALIDATION RESULTS:")
        original_entries_check = parse_srt_simple(open(backup_file, 'r', encoding='utf-8').read())
        
        if len(original_entries_check) == len(processed_entries):
            print("✅ Structure validation: PASSED")
            print(f"📊 Entry count: {len(processed_entries)}")
            
            # Check for timestamp preservation
            timestamp_issues = 0
            for i, (orig_entry, proc_entry) in enumerate(zip(original_entries_check, processed_entries)):
                if (orig_entry['start'] != proc_entry['start'] or 
                    orig_entry['end'] != proc_entry['end'] or 
                    orig_entry['index'] != proc_entry['index']):
                    timestamp_issues += 1
                    if timestamp_issues <= 3:  # Show first 3 issues
                        print(f"⚠️  Entry {i+1}: Timestamp/index mismatch")
            
            if timestamp_issues == 0:
                print("✅ All timestamps and indices preserved")
            else:
                print(f"❌ {timestamp_issues} timestamp/index issues found")
        else:
            print("❌ Structure validation: FAILED")
            print(f"🚨 Entry count mismatch: {len(original_entries_check)} vs {len(processed_entries)}")
        
        # Summary
        print(f"\n📋 FINAL SUMMARY:")
        print(f"  Original entries: {total_entries}")
        print(f"  Processed entries: {processed_total}")
        print(f"  Original pronouns: {total_pronouns}")
        print(f"  Processed pronouns: {processed_pronouns}")
        print(f"  Processing time: {processing_time:.2f} seconds")
        
        if total_entries == processed_total:
            print(f"  ✅ Entry count preserved")
        else:
            print(f"  ❌ Entry count mismatch!")
        
        if processed_pronouns < total_pronouns:
            replaced_pronouns = total_pronouns - processed_pronouns
            print(f"  🔄 Pronouns replaced: {replaced_pronouns}")
            replacement_rate = (replaced_pronouns / total_pronouns) * 100
            print(f"  📊 Replacement rate: {replacement_rate:.1f}%")
        
        # Copy processed file to Downloads
        shutil.copy2(original_file, output_file)
        print(f"\n📁 Files:")
        print(f"  Original (backup): {backup_file}")
        print(f"  Processed: {original_file}")
        print(f"  Output copy: {output_file}")
        
        # Show sample of processed content
        print(f"\n📄 SAMPLE PROCESSED CONTENT (first 10 entries):")
        print("=" * 70)
        sample_entries = processed_entries[:10]
        for entry in sample_entries:
            print(f"{entry['index']}")
            print(f"{entry['start']} --> {entry['end']}")
            print(f"{entry['text']}")
            print()
        print("=" * 70)
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # Restore backup if something went wrong
        if os.path.exists(backup_file):
            shutil.copy2(backup_file, original_file)
            print(f"🔄 Restored original file from backup")

if __name__ == "__main__":
    test_parallel_full_file()
