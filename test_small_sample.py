#!/usr/bin/env python3
"""
Test script for small sample to demonstrate the improved function works
"""

import os
import sys
import shutil
import re
from datetime import datetime

# Add the project root to Python path
sys.path.append('/Users/<USER>/PycharmProjects/discord')

try:
    from utils.gpt_service import OpenAIService
    from utils.helpers import load_gpt_prompt
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the project directory")
    sys.exit(1)

def parse_srt_simple(content):
    """Simple SRT parser"""
    import re
    
    # Split content into blocks
    blocks = re.split(r'\n\s*\n', content.strip())
    entries = []
    
    for block in blocks:
        if not block.strip():
            continue
            
        lines = block.strip().split('\n')
        if len(lines) < 3:
            continue
            
        try:
            # Parse index
            index = int(lines[0])
            
            # Parse timestamp
            timestamp_line = lines[1]
            if ' --> ' not in timestamp_line:
                continue
                
            start_time, end_time = timestamp_line.split(' --> ')
            
            # Parse text (everything after timestamp)
            text = '\n'.join(lines[2:])
            
            entries.append({
                'index': index,
                'start': start_time.strip(),
                'end': end_time.strip(),
                'text': text.strip()
            })
        except (ValueError, IndexError):
            continue
            
    return entries

def count_pronouns_in_text(text):
    """Count pronouns in text"""
    pronouns = ['he', 'she', 'him', 'her', 'his', 'hers', 'they', 'them', 'their', 'theirs']
    text_lower = text.lower()
    count = 0
    for pronoun in pronouns:
        # Use word boundaries to avoid partial matches
        pattern = r'\b' + re.escape(pronoun) + r'\b'
        count += len(re.findall(pattern, text_lower))
    return count

def test_small_sample():
    """Test with small sample"""
    print("🧪 Testing replace_pronoun_in_srt with small sample")
    print("=" * 60)
    
    # File paths
    original_file = "test_sample_small.srt"
    backup_file = "test_sample_small_backup.srt"
    
    if not os.path.exists(original_file):
        print(f"❌ Test file not found: {original_file}")
        return
    
    # Create backup
    shutil.copy2(original_file, backup_file)
    print(f"💾 Created backup: {backup_file}")
    
    try:
        # Analyze original file
        with open(original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        entries = parse_srt_simple(content)
        total_entries = len(entries)
        total_pronouns = sum(count_pronouns_in_text(entry['text']) for entry in entries)
        
        print(f"📈 Total SRT entries: {total_entries}")
        print(f"👤 Total pronouns found: {total_pronouns}")
        
        # Load system prompt
        system_prompt = load_gpt_prompt("replace_pronoun", "drama")
        print(f"✅ System prompt loaded ({len(system_prompt)} characters)")
        
        # Create OpenAI service instance
        service = OpenAIService(task_id="test_small", story_id="test_story_small")
        
        # Run the pronoun replacement
        print(f"\n🔄 Running pronoun replacement...")
        start_time = datetime.now()
        
        try:
            service.replace_pronoun_in_srt(original_file, system_prompt)
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            print(f"✅ Processing completed in {processing_time:.2f} seconds")
            
        except Exception as e:
            print(f"❌ Processing failed: {str(e)}")
            # Restore backup
            shutil.copy2(backup_file, original_file)
            print(f"🔄 Restored original file from backup")
            return
        
        # Analyze processed file
        with open(original_file, 'r', encoding='utf-8') as f:
            processed_content = f.read()
        
        processed_entries = parse_srt_simple(processed_content)
        processed_total = len(processed_entries)
        processed_pronouns = sum(count_pronouns_in_text(entry['text']) for entry in processed_entries)
        
        print(f"\n📊 RESULTS:")
        print(f"  Original entries: {total_entries}")
        print(f"  Processed entries: {processed_total}")
        print(f"  Original pronouns: {total_pronouns}")
        print(f"  Processed pronouns: {processed_pronouns}")
        
        if total_entries == processed_total:
            print(f"  ✅ Entry count preserved")
        else:
            print(f"  ❌ Entry count mismatch!")
        
        if processed_pronouns < total_pronouns:
            replaced_pronouns = total_pronouns - processed_pronouns
            print(f"  🔄 Pronouns replaced: {replaced_pronouns}")
        
        print(f"\n📄 PROCESSED CONTENT:")
        print("=" * 60)
        print(processed_content)
        print("=" * 60)
        
        print(f"\n📁 Files:")
        print(f"  Original (backup): {backup_file}")
        print(f"  Processed: {original_file}")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_small_sample()
