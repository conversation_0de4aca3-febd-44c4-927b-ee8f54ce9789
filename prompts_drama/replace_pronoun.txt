Process the SRT file to replace pronouns and relationship references while strictly maintaining subtitle structure. Process in multiple passes:

Pass 1 - Character and Context Mapping:
1. <PERSON>an entire SRT file to build:
   a) Character Map:
   {
     "Emma": {
       "relationships": {
         "roommate": "<PERSON>",
         "boyfriend": "<PERSON>"
       },
       "possessions": ["bed"]
     },
     "<PERSON>": {
       "role": "<PERSON>'s roommate"
     },
     "<PERSON>": {
       "role": "<PERSON>'s boyfriend"
     }
   }

   b) Sentence Continuation Map:
   - Track sentences that span multiple subtitles
   - Store complete sentences with their subtitle ranges
   Example:
   {
     "sentence_1": {
       "text": "<PERSON> heard her boyfriend Ted whisper",
       "spans": ["9", "10"],
       "complete": true
     }
   }

Pass 1.5 - Speaker and Action Ownership Analysis:
1. For each subtitle/line, identify and mark:
   a) Speaker Attribution:
      - Who is speaking in this line?
      - Is this dialogue or narration?
      - Mark dialogue attributions ("X said", "X replied")

   b) Action Ownership:
      - Who is performing each action?
      - Who owns each thought/feeling?
      - Who owns each possession?

   c) Reference Tracking:
      - Who is being referenced in each clause?
      - Track pronouns back to their antecedents

2. Create Action-Owner Map for each subtitle:
   Format:
   {
     "subtitle_id": {
       "speaker": "Character_Name",
       "action_owners": {
         "action1": "Character_Name",
         "thought1": "Character_Name",
         "possession1": "Character_Name"
       },
       "referenced_characters": ["Character_Name1", "Character_Name2"]
     }
   }

3. Example Analysis:
   "Remember how I told you I wanted to wait"
   Analysis:
   {
     "speaker": "Nathan",
     "action_owners": {
       "telling": "Nathan",
       "wanting": "<PERSON>"
     },
     "referenced_characters": ["Emma"]
   }
   Result: "Remember how <PERSON> told Emma Nathan wanted to wait"

4. Context Verification Steps:
   a) Identify dialogue markers ("said", "replied", "asked")
   b) Track conversation flow and speaker changes
   c) Map each action/thought to its owner
   d) Verify possessive relationships
   e) Cross-reference with character map

5. Error Prevention:
   - Always identify speaker before processing pronouns
   - Track dialogue attribution through quotation marks
   - Map each action to its owner before replacement
   - Verify ownership of thoughts and feelings
   - Double-check possessive relationships

6.  Narrative Voice Analysis:
    a. Identify narrative perspective:
       - First-person narrative ("I", "my", "we")
       - Third-person narrative
       - Mixed perspective

    b. For first-person narratives:
       - Identify the narrator character
       - Replace "I" with narrator's name in non-dialogue
       - Replace "my" with "[narrator's name]'s"
       - Maintain "I"/"my" in direct dialogue
       - Replace relationship references with names while maintaining narrative context

    c. Special handling:
       - Internal thoughts
       - Direct dialogue
       - Mixed narrative situations

Pass 2 - Processing Rules:

1. Pronoun Replacement:
   - Replace personal pronouns (he, she, they)
   - Replace possessive pronouns (his, her, their)
   - Replace object pronouns (him, her, them)
   - Replace relationship references with names

   DO NOT REPLACE:
   - First-person pronouns (I, my, we, our)

2. Critical Line Boundary Rules:
   - Never merge or move content between different SRT entries
   - Never modify subtitle numbers or timestamps
   - Keep all replacements within their original subtitle boundaries
   - Maintain exact line breaks as in original

3. Sentence Continuation Handling:
   - For sentences spanning multiple subtitles:
     * Analyze the complete sentence for context
     * Apply replacements only within original subtitle boundaries
     * Never move words between subtitles
     * Preserve partial phrases at line boundaries

4. Pronoun Resolution Priority:
   a) Use established character names
   b) Replace relationship references with names
   c) For possessives, determine ownership from context
   d) For plural pronouns, maintain subtitle boundaries

Example 1 - Spanning Sentence:
Original:
9
00:00:23,201 --> 00:00:25,521
never thought this would happen. Emma heard her

10
00:00:25,553 --> 00:00:27,945
boyfriend Ted whisper, emma says she's saving

Correct output :
9
00:00:23,201 --> 00:00:25,521
never thought this would happen. Emma heard        ← Maintain line break

10
00:00:25,553 --> 00:00:27,945
Ted whisper, Emma says she's saving

Example 2 - Multiple Characters:
Original:
1
00:00:02,889 --> 00:00:05,033
as she watched her roommate and boyfriend thrust

2
00:00:05,089 --> 00:00:08,265
against each other. They were in her bed, moaning

3
00:00:09,265 --> 00:00:011,265
against each other. When my stepmother walked in

Correct Output:
1
00:00:02,889 --> 00:00:05,033
as Emma watched Kristen and Ted thrust

2
00:00:05,089 --> 00:00:08,265
against each other. Kristen and Ted were in Emma's bed, moaning

3
00:00:05,089 --> 00:00:08,265
against each other. When Stacy walked in

Error Prevention:
1. Analyze complete sentences for context
2. But never:
   - Move words between subtitles
   - Merge subtitle content
   - Change subtitle timing
   - Modify line breaks
3. Keep pronouns unchanged when:
   - In direct quotes
   - Reference is ambiguous
   - Multiple possible interpretations exist

Validation Steps:
1. Compare subtitle numbers - must match original
2. Compare timestamps - must be identical
3. Verify line breaks occur at same positions
4. Check that no content has moved between subtitles
5. Verify each replacement stays in its original subtitle

CRITICAL REQUIREMENTS - FAILURE TO FOLLOW WILL RESULT IN REJECTION:
1. EXACT SUBTITLE COUNT: Output must have the same number of subtitle entries as input
   - If input has 69 subtitles, output MUST have exactly 69 subtitles
   - NEVER skip, merge, or drop any subtitle entry
   - Every single subtitle from input must appear in output
2. PRESERVE ALL TIMESTAMPS EXACTLY: Never modify any timestamp - keep them EXACTLY as provided
   - Format MUST remain: HH:MM:SS,mmm --> HH:MM:SS,mmm
   - Example: "00:00:27,320 --> 00:00:29,840" stays "00:00:27,320 --> 00:00:29,840"
   - DO NOT change to: "00:02:29,840" or "01:01:810" or any other format
3. PRESERVE SUBTITLE NUMBERS: Keep all subtitle index numbers exactly as in input
   - If input has subtitles 1, 2, 3... output must have 1, 2, 3... in same order
4. NO STRUCTURAL CHANGES: Never merge, split, or rearrange subtitle entries
5. CLEAN OUTPUT: Return only the processed SRT content without any markdown markers (```, srt, etc.)

TIMESTAMP PRESERVATION EXAMPLES:
✅ CORRECT: Keep exactly as input
Input:  "00:00:27,320 --> 00:00:29,840"
Output: "00:00:27,320 --> 00:00:29,840"

❌ WRONG: Any modification
"00:00:27,320 --> 00:02:29,840" (changed minutes)
"00:00:27,320 --> 00:00:29,84" (missing digit)
"00:00:27,320 --> 00:00:29:840" (wrong separator)

Remember:
- Do not include any surrounding markers like srt''', ```, or '''
- Subtitle integrity is highest priority - every input subtitle must have a corresponding output subtitle
- Context can span subtitles, but replacements cannot move content between subtitles
- Maintain all original formatting and structure exactly
- TIMESTAMPS MUST NEVER CHANGE - copy them character-for-character
- Process complete sentences for context but keep modifications within subtitle boundaries
- Return just the SRT content and nothing else
- If unsure about a pronoun reference, leave it unchanged rather than risk incorrect replacement