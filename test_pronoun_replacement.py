#!/usr/bin/env python3
"""
Test script for the improved replace_pronoun_in_srt function
"""

import os
import tempfile
import srt
from utils.gpt_service import OpenAIService
from utils.helpers import load_gpt_prompt

def create_test_srt():
    """Create a test SRT file with pronouns to replace"""
    test_srt_content = """1
00:00:01,000 --> 00:00:03,000
<PERSON> walked into the room. She looked around.

2
00:00:03,500 --> 00:00:06,000
Her boyfriend <PERSON> was sitting on the couch.

3
00:00:06,500 --> 00:00:09,000
He smiled at her and said hello.

4
00:00:09,500 --> 00:00:12,000
They both sat down together.

5
00:00:12,500 --> 00:00:15,000
<PERSON> told him about her day at work."""
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8') as f:
        f.write(test_srt_content)
        return f.name

def validate_srt_structure(original_file, processed_file):
    """Validate that the processed SRT maintains structure"""
    with open(original_file, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    with open(processed_file, 'r', encoding='utf-8') as f:
        processed_content = f.read()
    
    original_entries = list(srt.parse(original_content))
    processed_entries = list(srt.parse(processed_content))
    
    print(f"Original entries: {len(original_entries)}")
    print(f"Processed entries: {len(processed_entries)}")
    
    if len(original_entries) != len(processed_entries):
        print("❌ FAIL: Entry count mismatch!")
        return False
    
    # Check timestamps are preserved
    for i, (orig, proc) in enumerate(zip(original_entries, processed_entries)):
        if orig.start != proc.start or orig.end != proc.end:
            print(f"❌ FAIL: Timestamp mismatch in entry {i+1}")
            print(f"  Original: {orig.start} --> {orig.end}")
            print(f"  Processed: {proc.start} --> {proc.end}")
            return False
        
        if orig.index != proc.index:
            print(f"❌ FAIL: Index mismatch in entry {i+1}")
            return False
    
    print("✅ PASS: All structural validations passed!")
    return True

def test_pronoun_replacement():
    """Test the improved pronoun replacement function"""
    print("🧪 Testing improved replace_pronoun_in_srt function...")
    
    # Create test SRT file
    test_file = create_test_srt()
    print(f"Created test SRT file: {test_file}")
    
    try:
        # Load system prompt
        system_prompt = load_gpt_prompt("replace_pronoun", "drama")
        
        # Create OpenAI service instance
        service = OpenAIService(task_id="test_task", story_id="test_story")
        
        # Make a backup of original content
        with open(test_file, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        print("\nOriginal SRT content:")
        print("=" * 50)
        print(original_content)
        
        # Test the function
        print("\n🔄 Running pronoun replacement...")
        service.replace_pronoun_in_srt(test_file, system_prompt)
        
        # Read processed content
        with open(test_file, 'r', encoding='utf-8') as f:
            processed_content = f.read()
        
        print("\nProcessed SRT content:")
        print("=" * 50)
        print(processed_content)
        
        # Create temporary file with original content for validation
        with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8') as f:
            f.write(original_content)
            original_file = f.name
        
        # Validate structure
        print("\n🔍 Validating SRT structure...")
        is_valid = validate_srt_structure(original_file, test_file)
        
        if is_valid:
            print("\n✅ SUCCESS: Pronoun replacement completed successfully!")
        else:
            print("\n❌ FAILURE: Structure validation failed!")
        
        # Cleanup
        os.unlink(original_file)
        
    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup test file
        if os.path.exists(test_file):
            os.unlink(test_file)

if __name__ == "__main__":
    test_pronoun_replacement()
