"""
SRT validation utilities for pronoun replacement
"""

import srt
import re
from typing import List, Tu<PERSON>, Dict


def validate_srt_file(filename: str) -> Dict[str, any]:
    """
    Validate an SRT file and return detailed information
    
    Args:
        filename: Path to SRT file
        
    Returns:
        Dictionary with validation results
    """
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        entries = list(srt.parse(content))
        
        return {
            'valid': True,
            'entry_count': len(entries),
            'entries': entries,
            'content': content,
            'error': None
        }
    except Exception as e:
        return {
            'valid': False,
            'entry_count': 0,
            'entries': [],
            'content': '',
            'error': str(e)
        }


def compare_srt_files(original_file: str, processed_file: str) -> Dict[str, any]:
    """
    Compare two SRT files to ensure structure is preserved
    
    Args:
        original_file: Path to original SRT file
        processed_file: Path to processed SRT file
        
    Returns:
        Dictionary with comparison results
    """
    original = validate_srt_file(original_file)
    processed = validate_srt_file(processed_file)
    
    if not original['valid']:
        return {
            'valid': False,
            'error': f"Original file invalid: {original['error']}"
        }
    
    if not processed['valid']:
        return {
            'valid': False,
            'error': f"Processed file invalid: {processed['error']}"
        }
    
    # Check entry count
    if original['entry_count'] != processed['entry_count']:
        return {
            'valid': False,
            'error': f"Entry count mismatch: original={original['entry_count']}, processed={processed['entry_count']}"
        }
    
    # Check each entry
    issues = []
    for i, (orig_entry, proc_entry) in enumerate(zip(original['entries'], processed['entries'])):
        # Check index
        if orig_entry.index != proc_entry.index:
            issues.append(f"Entry {i+1}: Index mismatch ({orig_entry.index} vs {proc_entry.index})")
        
        # Check timestamps
        if orig_entry.start != proc_entry.start:
            issues.append(f"Entry {i+1}: Start time mismatch ({orig_entry.start} vs {proc_entry.start})")
        
        if orig_entry.end != proc_entry.end:
            issues.append(f"Entry {i+1}: End time mismatch ({orig_entry.end} vs {proc_entry.end})")
    
    return {
        'valid': len(issues) == 0,
        'error': '; '.join(issues) if issues else None,
        'original_count': original['entry_count'],
        'processed_count': processed['entry_count'],
        'issues': issues
    }


def analyze_pronoun_changes(original_file: str, processed_file: str) -> Dict[str, any]:
    """
    Analyze what pronoun changes were made
    
    Args:
        original_file: Path to original SRT file
        processed_file: Path to processed SRT file
        
    Returns:
        Dictionary with change analysis
    """
    original = validate_srt_file(original_file)
    processed = validate_srt_file(processed_file)
    
    if not original['valid'] or not processed['valid']:
        return {'valid': False, 'error': 'One or both files are invalid'}
    
    changes = []
    pronouns = ['he', 'she', 'him', 'her', 'his', 'hers', 'they', 'them', 'their']
    
    for i, (orig_entry, proc_entry) in enumerate(zip(original['entries'], processed['entries'])):
        orig_text = orig_entry.content.lower()
        proc_text = proc_entry.content.lower()
        
        if orig_text != proc_text:
            # Find specific changes
            orig_words = orig_text.split()
            proc_words = proc_text.split()
            
            # Simple word-by-word comparison
            if len(orig_words) == len(proc_words):
                for j, (orig_word, proc_word) in enumerate(zip(orig_words, proc_words)):
                    if orig_word != proc_word:
                        changes.append({
                            'entry': i + 1,
                            'position': j,
                            'original': orig_word,
                            'replacement': proc_word,
                            'is_pronoun': any(pronoun in orig_word.lower() for pronoun in pronouns)
                        })
            else:
                # Length changed, record as general change
                changes.append({
                    'entry': i + 1,
                    'original_text': orig_entry.content,
                    'processed_text': proc_entry.content,
                    'type': 'text_length_change'
                })
    
    return {
        'valid': True,
        'changes': changes,
        'total_changes': len(changes)
    }


def print_srt_comparison(original_file: str, processed_file: str):
    """
    Print a detailed comparison of two SRT files
    """
    print("🔍 SRT File Comparison")
    print("=" * 50)
    
    comparison = compare_srt_files(original_file, processed_file)
    
    if comparison['valid']:
        print("✅ Structure validation: PASSED")
        print(f"📊 Entry count: {comparison['original_count']}")
    else:
        print("❌ Structure validation: FAILED")
        print(f"🚨 Error: {comparison['error']}")
        return
    
    # Analyze changes
    changes = analyze_pronoun_changes(original_file, processed_file)
    
    if changes['valid']:
        print(f"🔄 Total changes made: {changes['total_changes']}")
        
        if changes['changes']:
            print("\n📝 Detailed changes:")
            for change in changes['changes'][:10]:  # Show first 10 changes
                if 'original_text' in change:
                    print(f"  Entry {change['entry']}: Text structure changed")
                else:
                    pronoun_marker = "👤" if change['is_pronoun'] else "📝"
                    print(f"  {pronoun_marker} Entry {change['entry']}: '{change['original']}' → '{change['replacement']}'")
            
            if len(changes['changes']) > 10:
                print(f"  ... and {len(changes['changes']) - 10} more changes")
    else:
        print(f"❌ Change analysis failed: {changes['error']}")
