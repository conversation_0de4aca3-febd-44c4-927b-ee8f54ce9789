import datetime
import json
import re
import time
from enum import Enum
from typing import List, Optional, Dict

import pysrt
# import srt  # Commented out - using custom parser instead
from openai import OpenAI, OpenAIError
from pydantic import BaseModel, Field

from exceptions.Exceptions import TerminateRetry
from llm.bedrock_strategy import extract_json
from llm.llm_models import LLMModels
from llm.llm_service import LLMService
from utils.aws_secrets import AWSSecrets
from sentry_sdk import capture_exception
from utils.helpers import retry, load_gpt_prompt, log_chatgpt_response
from utils.srt_utils import fix_srt_new_line_padding
from utils.token_utils import split_srt_into_batches
from utils.third_party_usage_service import ThirdPartyUsageTracker

client = OpenAI(api_key=AWSSecrets().get_openai_cred())
NUDGE_MESSAGE = "Your response is not as per the provided instructions, improve your response. Absolutely make sure all frames are present as requested."


class MessageType(Enum):
    INITIAL_MESSAGE = "initial_message"
    INITIAL_RESPONSE = "initial_response"
    NUDGE_MESSAGE = "nudge_message"
    FINAL_RESPONSE = "final_response"
    INITIAL_TOOL_CALL = "initial_tool_call"
    FINAL_TOOL_CALL = "final_tool_call"
    SYSTEM = "system"

class ChatHistory:
    def __init__(self) -> None:
        self.chat_messages = []

    def get_last_message(self):
        return self.chat_messages[-1] if self.chat_messages else {}

    def add_message(self, role, message, type, index=None):
        if self.get_last_message().get("type") == type:
            return
        new_message = {
            "role": role,
            "content": message,
            "type": type
        }
        if index is not None:
            if 0 <= index <= len(self.chat_messages):
                self.chat_messages.insert(index, new_message)
            else:
                raise IndexError(f"Index {index} out of range for chat history of length {len(self.chat_messages)}")
        else:
            self.chat_messages.append(new_message)

    def remove_last_message(self):
        self.chat_messages.pop()

    def remove_first_message(self):
        if self.chat_messages:
            self.chat_messages.pop(0)

    def build_conversation(self, user_exceptions=[]):
        messages = []
        allowed_types = [MessageType.INITIAL_MESSAGE.value,
                         MessageType.SYSTEM.value,
                         MessageType.FINAL_RESPONSE.value,
                         MessageType.FINAL_TOOL_CALL.value]
        allowed_types.extend(user_exceptions)
        for message in self.chat_messages:
            if message.get("type") in allowed_types:
                message_copy = message.copy()
                message_copy.pop("type", None)
                messages.append(message_copy)

        return messages

    def append(self, message):
        self.chat_messages.append(message)

    def get_message_history(self):
        messages = []
        for message in self.chat_messages:
            message_copy = message.copy()
            message_copy.pop("type", None)
            messages.append(message_copy)

        return messages

    def reset(self):
        self.chat_messages = []

    def reset_with_system(self, num_messages_to_keep=12):
        system_and_second = self.chat_messages[:2]
        remaining_messages = self.chat_messages[2:]
        while len(remaining_messages) > (num_messages_to_keep - 2):
            user_indices = [i for i, msg in enumerate(remaining_messages) if msg.get('role') == 'user']
            if len(user_indices) >= 2:
                start_idx = user_indices[1]
                remaining_messages = remaining_messages[start_idx:]
            else:
                remaining_messages = remaining_messages[-(num_messages_to_keep - 2):]
                break
        self.chat_messages = system_and_second + remaining_messages


class CharacterImage(BaseModel):
    first_name: str = Field(..., description="First Name of the character")
    gender: str = Field(..., description="Gender of the character")
    body_details: str = Field(..., description="description of the character's body details")
    short_prompt: str = Field(..., description="Shorter version of the character's body details")


class SceneInfo(BaseModel):
    start_time: str = Field(..., description="starting time of the scene")
    end_time: str = Field(..., description="end time of the scene")
    scene_description: str = Field(..., description="Background description of the scene")
    scene: int = Field(..., description="Scene index")


class CharactersList(BaseModel):
    characters: List[CharacterImage] = Field(..., description="list of all the characters in the story")


class SceneList(BaseModel):
    scenes: List[SceneInfo] = Field(..., description="list of all the scenes in the story")


class CharacterPrompt(BaseModel):
    first_name: str = Field(..., description="first name of the character")
    prompt: str = Field(..., description="character prompt defining its clothes and expressions")


class OutfitDetails(BaseModel):
    colour: str = Field(..., description="colour of the outfit")
    type: str = Field(..., description="type of the outfit")
    pattern: str = Field(..., description="pattern of the outfit")
    details: str = Field(..., description="details of the outfit")
    sleeves: str = Field(..., description="details of the sleeves")
    neck_line: str = Field(..., description="details of the neck_line")


class CharacterOutfit(BaseModel):
    character: str = Field(..., description="first name of the character")
    outfit_details: OutfitDetails = Field(..., description="outfit details of characters")


class CharacterOutfitList(BaseModel):
    character_outfits: List[CharacterOutfit] = Field(..., description="list of all outfit details of characters")


class HookPrompt(BaseModel):
    frame: str = Field(..., description="Frame and timestamp of the dialogue")
    theme: str = Field(..., description="Theme of the dialogue (e.g., abuse, action)")
    character: str = Field(..., description="Character token referenced in the prompt")
    reasoning_theme: str = Field(..., description="Reasoning behind the selected theme")
    prompt_reasoning: str = Field(..., description="Reasoning behind the visual prompt design")
    prompt: str = Field(..., description="Final image generation prompt with placeholders")


class HookPrompts(BaseModel):
    prompts: List[HookPrompt] = Field(..., description="list of all hook prompts for the srt dialogues")


class HeroCharacterInfo(BaseModel):
    name: Optional[str] = Field(..., description="first name of the character")
    face: Optional[str] = Field(..., description="facial expression of the character")
    gaze: Optional[str] = Field(..., description="gaze of the character")
    speaking: Optional[bool] = Field(..., description="whether the character is speaking or not")


class ImageV2(BaseModel):
    start_time: str = Field(..., description="starting time of the scene")
    hero_character_info: HeroCharacterInfo = Field(..., description="details of the hero character")
    prompt: str = Field(..., description="prompt of the image for the scene")
    camera_focus_point: str = Field(..., description="camera focus point for the scene")
    background: str = Field(..., description="background details of the scene")


class ImageV1(BaseModel):
    start_time: str = Field(..., description="starting time of the scene")
    background: str = Field(..., description="background image prompt for the scene")
    hero_character: str = Field(..., description="first name of the character to show for the scene")
    hero_character_outfit: str = Field(..., description="outfit details of the hero character")
    hero_character_expression: str = Field(..., description="expression of the hero character")
    hero_character_description: str = Field(..., description="description of the hero character's face and body")


class CharactersInStory(BaseModel):
    characters: List[str] = Field(..., description="list of all the characters in the story")


class ImageListV2(BaseModel):
    images: List[ImageV2] = Field(..., description="list of all image prompts for every scene in the script")


class ImageListV1(BaseModel):
    images: List[ImageV1] = Field(..., description="list of all image prompts for every scene in the script")


class SceneDescription(BaseModel):
    visual: str = Field(
        description="Visual description of the scene or character's appearance"
    )
    shot_category: str = Field(
        description="Category of the shot in the scene"
    )
    reason: str = Field(
        description="Reasoning behind the shot category selection"
    )
    dialogue: str = Field(
        description="Character's dialogue in the scene"
    )
    focus_character: str = Field(
        description="Main character in focus for this scene"
    )


class SceneScript(BaseModel):
    scenes: Dict[str, SceneDescription] = Field(
        description="Dictionary of scene descriptions, keyed by scene number"
    )


class EntityInfo(BaseModel):
    entity: str = Field(description="Entity name")
    entity_description: str = Field(description="Entity description of the scene")


class EntityList(BaseModel):
    entities: Dict[str, EntityInfo] = Field(description="list of all the entities in the story")


class SlugLine(BaseModel):
    index: int = Field(
        ...,
        description="index of the section"
    )
    start_time: str = Field(
        ...,
        description="Start time in format HH:MM:SS,mmm"
    )
    end_time: str = Field(
        ...,
        description="End time in format HH:MM:SS,mmm"
    )


class SlugLineList(BaseModel):
    slug_lines: List[SlugLine] = Field(
        ...,
        description="List of scenes with their descriptions and timestamps"
    )


class OpenAIService:
    def __init__(self, task_id, story_id, style=None, language="english") -> None:
        self.task_id = task_id
        self.story_id = story_id
        self.style = style
        self.language = language
        self.initial_prompt_done = {}

    def gpt3prompt2(self, messages, functions, mode, model=LLMModels.GPT_4O.value, retry_count=0):
        from utils.utils import add_message
        trials = 10
        arguments = None
        completion = None
        while trials:
            try:
                status = "SUCCESS" if retry_count == 0 and trials == 10 else "RETRY"
                completion = client.chat.completions.create(model=model,
                                                            messages=messages,
                                                            functions=functions,
                                                            temperature=0.5,
                                                            function_call="auto")
                ThirdPartyUsageTracker().track_usage(model, functions[0]["name"], completion.usage.prompt_tokens,
                                                     completion.usage.completion_tokens,
                                                     completion.usage.prompt_tokens_details.cached_tokens, status)
                log_chatgpt_response(messages, completion, datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S:%f"),
                                     mode, self.story_id, self.task_id, self.language)
                output_txt = completion.choices[0].message.function_call.arguments
                arguments = json.loads(output_txt)
                break
            except Exception as e:
                add_message(e)
                trials -= 1
                if isinstance(e, OpenAIError):
                    capture_exception(e)
                time.sleep(5 * (10 - trials))
                continue

        return {mode: []} if arguments is None else arguments

    def gpt3prompt2_new(self, messages, tools, mode, function_name, model=LLMModels.GPT_4O.value, retry_count=0):
        from utils.utils import add_message
        trials = 10
        completion = None
        while trials:
            try:
                status = "SUCCESS" if retry_count == 0 and trials == 10 else "RETRY"
                completion = client.chat.completions.create(
                    model=model,
                    messages=messages,
                    temperature=0.5,
                    tools=tools,
                    tool_choice={
                        "type": "function",
                        "function": {
                            "name": function_name,
                        }
                    }
                )
                ThirdPartyUsageTracker().track_usage(model, function_name, completion.usage.prompt_tokens,
                                                     completion.usage.completion_tokens,
                                                     completion.usage.prompt_tokens_details.cached_tokens, status)
                log_chatgpt_response(messages, completion, datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S:%f"),
                                     mode, self.story_id, self.task_id, self.language)
                break
            except Exception as e:
                if hasattr(e, "response") and e.response.status_code == 400:
                    raise TerminateRetry(e)
                add_message(e)
                trials -= 1
                if isinstance(e, OpenAIError):
                    capture_exception(e)
                time.sleep(5 * (10 - trials))
                continue

        return {mode: []} if completion is None else completion

    def gpt4prompt_wo_tool(self, messages, mode, model=LLMModels.GPT_4O.value):
        from utils.utils import add_message
        trials = 10
        completion = None
        while trials:
            try:
                completion = client.chat.completions.create(
                    model=model,
                    temperature=0.5,
                    messages=messages
                )
                status = "SUCCESS" if trials == 10 else "RETRY"
                ThirdPartyUsageTracker().track_usage(model, "gpt4prompt_wo_tool", completion.usage.prompt_tokens,
                                                     completion.usage.completion_tokens,
                                                     completion.usage.prompt_tokens_details.cached_tokens, status)
                log_chatgpt_response(messages, completion, datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S:%f"),
                                     mode, self.story_id, self.task_id, self.language)
                break
            except Exception as e:
                if hasattr(e, "response") and e.response.status_code == 400:
                    raise TerminateRetry(e)
                add_message(e)
                trials -= 1
                if isinstance(e, OpenAIError):
                    capture_exception(e)
                time.sleep(5 * (10 - trials))
                continue

        return {mode: []} if completion is None else completion.choices[0].message.content

    def find_characters_in_story(self, story_text, retry_count):
        prompt = f'''
Return the list of named characters in the following story:
{story_text}
'''
        messages = ChatHistory()
        messages.add_message(role="user", message=prompt, type=MessageType.INITIAL_MESSAGE.value)
        functions = [
                        {
                            "name": "find_characters_in_story",
                            "description": "Find characters in the story",
                            "parameters": CharactersInStory.model_json_schema()
                        }
                    ]
        characters = self.gpt3prompt2(messages.get_message_history(), functions, "characters", retry_count=retry_count)
        messages.add_message(role="assistant", message="", type=MessageType.INITIAL_RESPONSE.value)
        return characters.get("characters", [])

    @retry(max_retries=3, delay=10)
    def get_characters_prompt(self, script: str, messages: ChatHistory, retry_count=0):
        from utils.utils import add_exception
        batches = []
        batch = ""
        for sent in script.split("."):
            sent += "."
            batch += sent
            if len(batch) > 12800:
                batches.append(batch)
                batch = ""

        if batch:
            batches.append(batch)

        characters = {"characters": []}
        for batch in batches:
            found_characters = self.find_characters_in_story(batch, retry_count)
            prompt = f'''
Provide prompts for characters {str(found_characters) if found_characters else ''} from the following script:
{batch}
'''
            messages.add_message(role="user", message=prompt, type=MessageType.NUDGE_MESSAGE.value)
            functions = [
                            {
                                "name": "get_characters_prompts",
                                "description": "Get character prompts with their name, gender, body details and short prompt",
                                "parameters": CharactersList.model_json_schema()
                            }
                        ]
            characters_temp = self.gpt3prompt2(messages.get_message_history(), functions, "characters", retry_count=retry_count)
            characters["characters"].extend(characters_temp.get("characters", []))
            messages.add_message(role="assistant", message="", type=MessageType.FINAL_RESPONSE.value)
            messages.reset_with_system()

        if not character_prompt_sanity(characters.get("characters", [])):
            add_exception("Character prompt generated was not in correct format")
            messages.remove_last_message()
            raise Exception("Invalid character prompts generated")

        return characters

    @retry(max_retries=10, delay=10)
    def generate_scenes_and_background_details(self, script: str, system_prompt: str, retry_count=0):
        from utils.utils import add_message
        messages = ChatHistory()
        messages.add_message(role="system",
                             message=f"This is the system message that provides general instructions regarding you(AI) and the nature of the conversation : {system_prompt}",
                             type=MessageType.SYSTEM.value)

        prompt = (f"Return a list of scenes (with start time, end time, description, and scene index)"
                  f" from the following story:\n{script}")
        messages.add_message(role="user", message=prompt, type=MessageType.INITIAL_MESSAGE.value)
        llm_call_type = "SUCCESS" if retry_count == 0 else "RETRY"
        response_content = LLMService("gemini").generate_response(messages, LLMModels.GEMINI_TWO_POINT_5_FLASH.value, 0.5,
                                                                  tools=SceneList, llm_call_type=llm_call_type)
        scenes = response_content.model_dump()
        messages.add_message(role="assistant", message="", type=MessageType.FINAL_RESPONSE.value)
        messages.reset_with_system()

        if not check_scene_response_sanity(scenes.get("scenes", []), script):
            add_message("Response scenes sanity check failed, retrying with nudge message..")
            messages.remove_last_message()
            nudge_message = (
                "The start time of the first scene in the response does not match the start time of the input script and the end time of the last scene in the response does not match the end time of the input script. "
                "Be mindful of the format of the response and start and end time of the scene while categorizing them.")
            messages.add_message(role="user", message=nudge_message, type=MessageType.NUDGE_MESSAGE.value)
            response_content = LLMService("gemini").generate_response(messages, LLMModels.GEMINI_TWO_POINT_5_FLASH.value,
                                                                      0.5, tools=SceneList, llm_call_type=llm_call_type)
            scenes = response_content.model_dump()
            if scenes.get("scenes"):
                scenes_list = match_scenes_start_end_times_with_srt(scenes=scenes.get("scenes"), srt_content=script)
                scenes["scenes"] = scenes_list
                messages.add_message(role="assistant", message="", type=MessageType.FINAL_RESPONSE.value)
                messages.reset_with_system()
        if not scenes['scenes']:
            raise Exception("Scenes not generated")
        scenes = sorted(scenes['scenes'], key=lambda x: x['scene'])
        scenes[0]["start_time"] = "00:00:00,000"
        return scenes

    @retry(max_retries=10, delay=10)
    def generate_entities_details(self, script: str, system_prompt: str, retry_count=0):
        messages = ChatHistory()
        messages.add_message(role="system",
                             message=f"This is the system message that provides general instructions regarding you(AI) and the nature of the conversation : {system_prompt}",
                             type="system")

        prompt = (
            f"Return a list of entities (with entities and their description). Give direct output without adding anything in the start. Stick to the JSON output defined in the system prompt"
            f" from the following story:\n{script}")
        messages.add_message(role="user", message=prompt, type=MessageType.INITIAL_MESSAGE.value)
        llm_call_type = "SUCCESS" if retry_count == 0 else "RETRY"
        response_content = LLMService("claude").generate_response(messages,
                                                                  LLMModels.CLAUDE_3_POINT_5_SONNET.value,
                                                                  0.5, tools=EntityList, llm_call_type=llm_call_type,
                                                                  call_identifier="generate_entities_details")
        entities_json = response_content.get("entities")
        entities = []
        if entities_json:
            for entity, details in entities_json.items():
                entities.append(details)
        messages.add_message(role="assistant", message="", type=MessageType.FINAL_RESPONSE.value)
        messages.reset_with_system()

        return entities

    @retry(max_retries=3, delay=10)
    def generate_character_outfit(self, script: str, system_prompt: str, retry_count=0):
        from utils.utils import add_exception
        messages = ChatHistory()
        messages.add_message(role="system", message=system_prompt, type=MessageType.SYSTEM.value)

        prompt = f"Return a list of character outfits from the following story:\n{script}"
        messages.add_message(role="user", message=prompt, type=MessageType.INITIAL_MESSAGE.value)
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "generate_character_outfit",
                    "description": "Character outfit details",
                    "parameters": CharacterOutfitList.model_json_schema(),
                    "required": ["character_outfits"]
                }
            }
        ]
        completion = self.gpt3prompt2_new(messages.get_message_history(), tools, "character_outfits",
                                          "generate_character_outfit", model=LLMModels.GPT_4O.value, retry_count=retry_count)
        tool_call = completion.choices[0].message.tool_calls[0]
        arguments = tool_call.function.arguments
        char_outfits = json.loads(arguments)
        messages.add_message(role="assistant", message="", type=MessageType.FINAL_RESPONSE.value)
        messages.reset_with_system()

        is_correct_outfit, err_msg = scene_char_outfit_prompt_sanity(char_outfits.get("character_outfits", []))
        if not is_correct_outfit:
            add_exception(f"Character outfit details generated were not in correct format - Error : {err_msg}")
            messages.remove_last_message()
            raise Exception(f"Character outfit details generated were not in correct format - Error : {err_msg}")
        return char_outfits

    def _parse_srt_content(self, srt_content: str):
        """Custom SRT parser that doesn't rely on external srt module"""
        import re

        # Split content into blocks
        blocks = re.split(r'\n\s*\n', srt_content.strip())
        entries = []

        for block in blocks:
            if not block.strip():
                continue

            lines = block.strip().split('\n')
            if len(lines) < 3:
                continue

            try:
                # Parse index
                index = int(lines[0])

                # Parse timestamp
                timestamp_line = lines[1]
                if ' --> ' not in timestamp_line:
                    continue

                start_time, end_time = timestamp_line.split(' --> ')

                # Parse text (everything after timestamp)
                text = '\n'.join(lines[2:])

                entries.append({
                    'index': index,
                    'start': start_time.strip(),
                    'end': end_time.strip(),
                    'text': text.strip()
                })
            except (ValueError, IndexError):
                continue

        return entries

    def _fix_timestamps_in_response(self, response_content: str, original_entries: list):
        """Fix timestamp issues in LLM response by replacing with original timestamps"""
        import re

        response_entries = self._parse_srt_content(response_content)

        if len(response_entries) != len(original_entries):
            return response_content  # Can't fix if count mismatch

        # Create a mapping of original timestamps
        timestamp_map = {}
        for orig_entry in original_entries:
            timestamp_map[orig_entry['index']] = f"{orig_entry['start']} --> {orig_entry['end']}"

        # Fix timestamps in response
        fixed_blocks = []
        for resp_entry in response_entries:
            if resp_entry['index'] in timestamp_map:
                # Use original timestamp
                correct_timestamp = timestamp_map[resp_entry['index']]
                fixed_block = f"{resp_entry['index']}\n{correct_timestamp}\n{resp_entry['text']}"
                fixed_blocks.append(fixed_block)
            else:
                # Keep as is if we can't find original
                fixed_block = f"{resp_entry['index']}\n{resp_entry['start']} --> {resp_entry['end']}\n{resp_entry['text']}"
                fixed_blocks.append(fixed_block)

        return "\n\n".join(fixed_blocks)

    def _process_batch_parallel(self, batch_data):
        """Process a single batch - designed for parallel execution"""
        batch_idx, batch, system_prompt, retry_count = batch_data

        # Parse current batch to get expected count
        batch_entries = self._parse_srt_content(batch)
        batch_count = len(batch_entries)

        if batch_count == 0:
            return batch_idx, "", 0

        llm_service = LLMService("gemini")
        messages = ChatHistory()
        messages.add_message(role="system", message=system_prompt, type=MessageType.SYSTEM.value)
        messages.add_message(role="user", message=batch, type=MessageType.INITIAL_MESSAGE.value)

        llm_call_type = "RETRY" if retry_count > 0 else "SUCCESS"

        max_retries = 3
        for attempt in range(max_retries):
            try:
                response_content = llm_service.generate_response(messages, LLMModels.GEMINI_TWO_POINT_5_FLASH.value, 1,
                                                                 llm_call_type=llm_call_type, call_identifier="replace_pronoun_in_srt")

                # Clean response content
                response_content = response_content.replace("```srt", "").replace("```", "").strip()

                # Fix timestamps in response using original timestamps
                response_content = self._fix_timestamps_in_response(response_content, batch_entries)

                # Validate response structure
                try:
                    response_entries = self._parse_srt_content(response_content)
                    response_count = len(response_entries)
                except Exception as e:
                    raise ValueError(f"Invalid SRT format in LLM response. Error: {str(e)}")

                # Check if counts match
                if response_count != batch_count:
                    raise ValueError(f"Length mismatch. LLM output has {response_count} subtitles, "
                                   f"but original batch has {batch_count} subtitles. All subtitles must be preserved.")

                # Validate timestamps are now preserved (should pass after fixing)
                for orig_entry, resp_entry in zip(batch_entries, response_entries):
                    if orig_entry['start'] != resp_entry['start'] or orig_entry['end'] != resp_entry['end']:
                        raise ValueError(f"Timestamp mismatch in subtitle {orig_entry['index']} after fixing. "
                                       f"Original: {orig_entry['start']} --> {orig_entry['end']}, "
                                       f"Response: {resp_entry['start']} --> {resp_entry['end']}")

                    if orig_entry['index'] != resp_entry['index']:
                        raise ValueError(f"Index mismatch in subtitle. "
                                       f"Original: {orig_entry['index']}, Response: {resp_entry['index']}")

                return batch_idx, response_content, response_count

            except Exception as e:
                if attempt == max_retries - 1:
                    raise ValueError(f"Batch {batch_idx + 1} failed after {max_retries} attempts: {str(e)}")
                import time
                time.sleep(1 * (attempt + 1))  # Exponential backoff
                continue

    @retry(max_retries=3, delay=1)
    def replace_pronoun_in_srt(self, srt_filename: str, system_prompt: str, retry_count=0):
        from concurrent.futures import ThreadPoolExecutor, as_completed
        import time

        # Read original SRT file
        with open(srt_filename, 'r', encoding='utf-8') as f:
            srt_file_content = f.read()

        # Parse original SRT to get baseline count
        original_srt_entries = self._parse_srt_content(srt_file_content)
        original_count = len(original_srt_entries)

        if original_count == 0:
            raise ValueError("Original SRT file is empty or invalid")

        batches = split_srt_into_batches(srt_file_content, 8192 - 4000, LLMModels.GEMINI_TWO_POINT_5_FLASH.value)

        print(f"Processing {len(batches)} batches in parallel...")

        # Prepare batch data for parallel processing
        batch_data_list = []
        for batch_idx, batch in enumerate(batches):
            batch_data_list.append((batch_idx, batch, system_prompt, retry_count))

        # Process batches in parallel
        processed_results = {}
        total_processed_entries = 0

        # Use ThreadPoolExecutor for parallel processing
        max_workers = min(5, len(batches))  # Limit concurrent requests to avoid rate limiting

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all batch processing tasks
            future_to_batch = {executor.submit(self._process_batch_parallel, batch_data): batch_data[0]
                              for batch_data in batch_data_list}

            # Collect results as they complete
            for future in as_completed(future_to_batch):
                batch_idx = future_to_batch[future]
                try:
                    batch_idx, response_content, response_count = future.result()
                    processed_results[batch_idx] = response_content
                    total_processed_entries += response_count
                    print(f"✅ Batch {batch_idx + 1} completed ({response_count} entries)")
                except Exception as e:
                    print(f"❌ Batch {batch_idx + 1} failed: {str(e)}")
                    raise ValueError(f"Batch {batch_idx + 1} processing failed: {str(e)}")

        # Combine all processed batches in correct order
        processed_batches = []
        for i in range(len(batches)):
            if i in processed_results:
                processed_batches.append(processed_results[i])

        final_srt = "\n\n".join(processed_batches)

        # Final validation
        try:
            final_entries = self._parse_srt_content(final_srt)
            final_count = len(final_entries)
        except Exception as e:
            raise ValueError(f"Final SRT is invalid. Error: {str(e)}")

        if final_count != original_count:
            raise ValueError(f"Final validation failed: Output has {final_count} subtitles, "
                           f"but original had {original_count} subtitles.")

        print(f"🎉 Successfully processed {final_count} entries in parallel!")

        # Write the processed SRT back to file
        with open(srt_filename, 'w', encoding='utf-8') as srt_file:
            srt_file.write(final_srt)

        fix_srt_new_line_padding(srt_filename)

    def format_scenes_message(self, timestamps):
        max_scenes = len(timestamps)
        message = "Scenes must be at "
        for i, timestamp in enumerate(timestamps[:max_scenes]):
            if i > 0:
                message += ", "
            message += f"{timestamp.split(',')[0]} sec"
        if len(timestamps) > max_scenes:
            message += f", only provide {max_scenes} scenes"
        return message

    def generate_next_timestamp(self, timestamps):
        if len(timestamps) < 2:
            return timestamps
        from datetime import datetime
        fmt, t = "%H:%M:%S,%f", timestamps[-2:]
        delta = datetime.strptime(t[1], fmt) - datetime.strptime(t[0], fmt)
        next_time = (datetime.strptime(t[1], fmt) + delta).strftime(fmt)[:-3]
        return timestamps + [next_time]

    def format_scenes_message_with_dialogue(self, batch_dict, word_timestamps_filename, scenes):
        from utils.utils import extract_subs_by_timestamp_range
        timestamps = batch_dict["image_timestamps"]
        if word_timestamps_filename:
            message = "Providing Frame number, it's start time, end time and dialogues it covers: \n"
            with open(word_timestamps_filename, "r", encoding="utf-8") as f:
                words = json.load(f)
                extended_timestamps = self.generate_next_timestamp(timestamps)

                if len(extended_timestamps) == 1:
                    index = 0
                    subs = extract_subs_by_timestamp_range(words, extended_timestamps[index], batch_dict["end_time"])
                    message += f"Frame {index + 1}: {extended_timestamps[index]} dialogue: {subs}\n"

                for index, ele in enumerate(extended_timestamps[:-1]):
                    subs = extract_subs_by_timestamp_range(
                        words,
                        extended_timestamps[index],
                        extended_timestamps[index + 1]
                    )
                    message += (f"Frame {index + 1}: {extended_timestamps[index]} -> "
                                f"{extended_timestamps[index + 1]}, dialogue: {subs}\n")

        else:
            max_scenes = len(timestamps)
            message = "Scenes must be at "
            for i, timestamp in enumerate(timestamps[:max_scenes]):
                if i > 0:
                    message += ", "
                message += f"{timestamp.split(',')[0]} sec"
            if len(timestamps) > max_scenes:
                message += f", only provide {max_scenes} scenes"
        return message

    @retry(max_retries=10, delay=10)
    def get_initial_scene_prompts(self, batch_dict: dict, messages: ChatHistory, moments_message: ChatHistory, genre: str, translation_required: bool = False,
                                  word_timestamps_filename=None, scenes=None, tools=None, retry_count=0):
        text = batch_dict['dialogues_with_timestamps']
        if messages.get_message_history()[-1]["content"] == NUDGE_MESSAGE:
            return None

        if translation_required:
            text = self.get_translated_srt(text=text, genre=genre, retry_count=retry_count)
        from utils.utils import add_message
        add_message("Starting format_scenes_message_with_dialogue")
        frame_prompt = self.format_scenes_message_with_dialogue(batch_dict,
                                                                word_timestamps_filename, scenes)
        frame_key_moment_prompt = self.identify_key_moments_in_frames(frame_prompt, moments_message, retry_count)
        prompt = f'''Story segment for context.
                {text}\n
                {frame_prompt + str(frame_key_moment_prompt) + "generate output for all json nodes"}
                '''
        messages.add_message(role="user", message=prompt, type=MessageType.INITIAL_MESSAGE.value)
        chat_to_send = messages.build_conversation()
        prompts, name, tool_call, arguments = self.generate_initial_prompt(chat_to_send, tools,
                                                                           model=LLMModels.GPT_4O.value,
                                                                           retry_count=retry_count)

        messages.append({
            "role": "assistant",
            "content": "",
            "tool_calls": [
                {
                    "id": tool_call.id,
                    "type": "function",
                    "function": {
                        "name": name,
                        "arguments": arguments
                    }
                }
            ],
            "type": MessageType.INITIAL_RESPONSE.value
        })
        messages.append({
            "role": "tool",
            "content": arguments,
            "tool_call_id": tool_call.id,
            "type": MessageType.INITIAL_TOOL_CALL.value
        })
        self.add_nudge_message_to_chat_history(messages)
        return prompts

    def generate_initial_prompt(self, chat_to_send, tools, model, retry_count):
        completion = self.gpt3prompt2_new(chat_to_send, tools, "images", "get_scene_prompts", model=model,
                                          retry_count=retry_count)
        tool_call = completion.choices[0].message.tool_calls[0]
        arguments = tool_call.function.arguments
        name = tool_call.function.name
        prompts = json.loads(arguments)
        return prompts, name, tool_call, arguments

    def get_scene_prompts(self, batch_dict: dict, messages: ChatHistory, moments_message: ChatHistory, genre: str, translation_required: bool = False,
                          version: str = "v2", word_timestamps_filename=None, scenes=None):
        max_retries = 10
        delay = 10
        image_list = ImageListV2 if version == "v2" else ImageListV1
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "get_scene_prompts",
                    "description": "Get prompts for scenes with background prompts and hero character details",
                    "parameters": image_list.model_json_schema(),
                    "required": ["images"]
                }
            }
        ]
        from utils.utils import add_exception, add_message
        completion = None
        tool_call = None
        arguments = None
        name = None
        prompts = None

        self.get_initial_scene_prompts(batch_dict, messages, moments_message, genre, translation_required, word_timestamps_filename,
                                       scenes, tools)

        for attempt in range(max_retries):
            try:
                try:
                    completion = self.gpt3prompt2_new(messages.get_message_history(), tools, "images",
                                                      "get_scene_prompts",
                                                      model=LLMModels.GPT_4O.value, retry_count=attempt)

                    tool_call = completion.choices[0].message.tool_calls[0]
                    arguments = tool_call.function.arguments
                    name = tool_call.function.name
                    prompts = json.loads(arguments)
                except Exception as e:
                    add_exception(e)
                    raise Exception("Invalid scene prompts generated in scene prompts")
                result, message = scene_prompt_sanity(prompts.get("images", []), batch_dict, messages, version=version)
                if not result:
                    add_message(message)
                    raise Exception(f"Invalid scene prompts generated in scene prompts {message}")
                break

            except Exception:
                if attempt < max_retries - 1:
                    time.sleep(delay)

        self.append_scene_prompt_response(arguments, messages, name, tool_call)
        self.manage_message_history(completion, messages)
        return prompts

    def add_nudge_message_to_chat_history(self, messages):
        nudge_message_anime, nudge_message_cinematic = NUDGE_MESSAGE, NUDGE_MESSAGE
        nudge_message = nudge_message_anime if self.style in ["anime", "anime_xl"] else nudge_message_cinematic
        messages.add_message(role="user", message=nudge_message, type=MessageType.NUDGE_MESSAGE.value)

    def manage_message_history(self, completion, messages):
        if len(messages.get_message_history()) > 13:
            messages.reset_with_system(num_messages_to_keep=12)
        if completion is not None and completion.usage.total_tokens >= 100000:
            messages.reset_with_system(num_messages_to_keep=6)

    def append_scene_prompt_response(self, arguments, messages, name, tool_call):
        messages.append({
            "role": "assistant",
            "content": "",
            "tool_calls": [
                {
                    "id": tool_call.id,
                    "type": "function",
                    "function": {
                        "name": name,
                        "arguments": arguments
                    }
                }
            ],
            "type": MessageType.FINAL_RESPONSE.value
        })
        messages.append({
            "role": "tool",
            "content": arguments,
            "tool_call_id": tool_call.id,
            "type": MessageType.FINAL_TOOL_CALL.value
        })

    def get_paraphrased_prompt(self, text: str, response_text: str):
        prompt = f"""
        Please paraphrase the original prompt to remove explicit or sensitive content, changing only the parts that triggered the issue while preserving the rest of the prompt.
        If you encounter errors related to inappropriate references to children or minors, consider increasing their age. For example, if the age is between 17–21, adjust it to 22. Replace terms like 'girl' with 'woman' or 'boy' with 'man'.

        For errors involving references to blood or death in a child context:  
        Example:  
        Input Prompt: Aerial view, moon, dead baby lying in dirt beneath blood moon, ageless female, glowing silver eyes, long flowing hair, moonlit skin, tall slender build, a moonlit forest clearing under a red moon, ash drifting in the air, a crying infant lies surrounded by snarling wolves that do not bite, in a werewolf-themed setting with medieval, Victorian, or gothic elements, cool blue tones.  
        Output Prompt: Aerial view, moon, sleeping baby lying peacefully on the forest floor beneath a crimson-hued moon, ageless female, glowing silver eyes, long flowing hair, moonlit skin, tall slender build, a moonlit forest clearing under a red moon, ash drifting in the air, a quietly resting infant surrounded by watchful wolves that do not bite, in a werewolf-themed setting with medieval, Victorian, or gothic elements, cool blue tones.

        For errors referencing the word 'slave', replace it with 'servant'.

        Replace vowels in the triggering words like: 
            -'dead': 'de@d'
            -'war': 'w@r'
            -'battle': 'b@ttle'
            -'blood': 'blud'
            -'shoot': 'shOOt'

        The following original prompt caused an NSFW error:  
        {text}

        Response: {response_text}
        
        Rephrase the prompt. Do not add something in the beginning:
        """
        messages = ChatHistory()
        messages.add_message("user", prompt, MessageType.INITIAL_MESSAGE.value)
        completion = None
        trials = 10
        while trials:
            try:
                completion = client.chat.completions.create(model=LLMModels.GPT_4O.value,
                                                            messages=messages.get_message_history())
                ThirdPartyUsageTracker().track_usage(LLMModels.GPT_4O.value, "paraphrased_prompt",
                                                     completion.usage.prompt_tokens,
                                                     completion.usage.completion_tokens,
                                                     completion.usage.prompt_tokens_details.cached_tokens, "RETRY")
                log_chatgpt_response(messages.get_message_history(), completion,
                                     datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S:%f"), "paraphrase",
                                     self.story_id, self.task_id, self.language)
                return completion.choices[0].message.content
            except Exception as e:
                trials -= 1
                if isinstance(e, OpenAIError):
                    capture_exception(e)
                time.sleep(5 * (10 - trials))
                continue
    
    def get_translated_srt(self, text: str, genre: str, retry_count: int):
        SRT_TRANSLATION_PROMPT = load_gpt_prompt("srt_translation", genre)
        prompt = f'''
        Translate these subtitles to English language:
        {text}
        '''
        messages = ChatHistory()
        messages.add_message("system", SRT_TRANSLATION_PROMPT, "system")
        messages.add_message("user", prompt, MessageType.INITIAL_MESSAGE.value)
        completion = None
        trials = 10
        while trials:
            try:
                completion = client.chat.completions.create(model=LLMModels.GPT_4O.value,
                                                            messages=messages.get_message_history())
                status = "SUCCESS" if retry_count == 0 and trials == 10 else "RETRY"
                ThirdPartyUsageTracker().track_usage(LLMModels.GPT_4O.value, "srt_translation",
                                                     completion.usage.prompt_tokens,
                                                     completion.usage.completion_tokens,
                                                     completion.usage.prompt_tokens_details.cached_tokens, status)
                log_chatgpt_response(messages.get_message_history(), completion,
                                     datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S:%f"), "translation",
                                     self.story_id, self.task_id, self.language)
                return completion.choices[0].message.content
            except Exception as e:
                trials -= 1
                if isinstance(e, OpenAIError):
                    capture_exception(e)
                time.sleep(5 * (10 - trials))
                continue

    def get_final_prompts(self, texts: str, genre: str, batch_dict: dict, style: str, slug_lines_background: bool , is_char_lora: bool = False):
        if style == 'imagen':
            prompt_key, prompt_genre = "rephrase_prompt_imagen", "fantasy"
        elif is_char_lora:
            prompt_key, prompt_genre = "rephrase_prompt_lora", genre
        elif slug_lines_background:
            prompt_key, prompt_genre = "rephrase_prompt_slug_lines", genre
        else:
            prompt_key, prompt_genre = "rephrase_prompt", genre

        FINAL_REPHRASE_PROMPT = load_gpt_prompt(prompt_key, prompt_genre)

        final_prompt = f'''Story segment for context. {batch_dict}\n
        Rephrase the image prompt. Do not add something in the beginning:
        {texts}
        '''
        messages = ChatHistory()
        messages.add_message("system", FINAL_REPHRASE_PROMPT, "system")
        messages.add_message("user", final_prompt, MessageType.INITIAL_MESSAGE.value)

        trials = 5
        while trials:
            try:
                completion = client.chat.completions.create(
                    model=LLMModels.GPT_4O.value,
                    messages=messages.get_message_history()
                )
                status = "SUCCESS" if trials == 5 else "RETRY"
                ThirdPartyUsageTracker().track_usage(LLMModels.GPT_4O.value, "get_final_prompts",
                                                     completion.usage.prompt_tokens,
                                                     completion.usage.completion_tokens,
                                                     completion.usage.prompt_tokens_details.cached_tokens, status)
                log_chatgpt_response(
                    messages.get_message_history(), completion,
                    datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S:%f"), "translation",
                    self.story_id, self.task_id, self.language
                )
                response_content = completion.choices[0].message.content.strip()
                return response_content
            except Exception as e:
                trials -= 1
                if isinstance(e, OpenAIError):
                    capture_exception(e)
                time.sleep(5 * (10 - trials))
        return texts

    def get_theme(self, text: str):
        FINAL_REPHRASE_PROMPT = '''"Analyze the given script and extract its core theme in no more than five words. Focus only on broad thematic elements like 'Medieval warfare,' 'Dragon clans conflict,' or 'Vampire political intrigue.' Avoid extra details or summaries.'''
        final_prompt = f'''Keep the theme short and precise. Do not add something in the beginning: {text}
        '''
        messages = ChatHistory()
        messages.add_message("system", FINAL_REPHRASE_PROMPT, "system")
        messages.add_message("user", final_prompt, MessageType.INITIAL_MESSAGE.value)

        trials = 5
        while trials:
            try:
                completion = client.chat.completions.create(
                    model=LLMModels.GPT_4O_MINI.value,
                    messages=messages.get_message_history()
                )
                status = "SUCCESS" if trials == 5 else "RETRY"
                ThirdPartyUsageTracker().track_usage(LLMModels.GPT_4O_MINI.value, "theme",
                                                     completion.usage.prompt_tokens,
                                                     completion.usage.completion_tokens,
                                                     completion.usage.prompt_tokens_details.cached_tokens, status)
                log_chatgpt_response(
                    messages.get_message_history(), completion,
                    datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S:%f"), "theme",
                    self.story_id, self.task_id, self.language
                )
                response_content = completion.choices[0].message.content.strip()
                return response_content
            except Exception as e:
                trials -= 1
                if isinstance(e, OpenAIError):
                    capture_exception(e)
                time.sleep(5 * (10 - trials))
        return response_content

    def identify_key_moments_in_frames(self, frame_prompt, moments_chat_message, retry_count):
        NO_INITIAL_STRING = '''Give direct output without adding anything in the start. Stick to the JSON output defined in the system prompt'''
        from utils.utils import add_message
        moments_chat_message.add_message("user", frame_prompt, MessageType.INITIAL_MESSAGE.value)
        trials = 5
        key_moments_response = None
        while trials:
            try:
                llm_call_type = "SUCCESS" if retry_count == 0 and trials == 5 else "RETRY"
                key_moments_result = LLMService("claude").generate_response(moments_chat_message,
                                                                            LLMModels.CLAUDE_3_POINT_5_SONNET.value,
                                                                            0.5, tools=SceneScript,
                                                                            llm_call_type=llm_call_type)
                if "scenes" in key_moments_result:
                    key_moments_response = key_moments_result.get("scenes")
                elif "text" in key_moments_result:
                    key_moments_response = extract_json(key_moments_result.get("text"))

                if not key_moments_response:
                    raise Exception("None in response")
                moments_chat_message.add_message("assistant", json.dumps(key_moments_response),
                                                 type=MessageType.INITIAL_RESPONSE.value)

                def check_consecutive_characters(frames):
                    for i in range(len(frames) - 2):
                        char1 = frames[str(i + 1)]['focus_character']
                        char2 = frames[str(i + 2)]['focus_character']
                        char3 = frames[str(i + 3)]['focus_character']
                        if char1 is not None and char1 == char2 == char3:
                            error = f"Frames {i + 1}, {i + 2}, {i + 3} have same character: {char1}"
                            from utils.utils import add_message
                            add_message(error)
                            moments_chat_message.add_message("user",
                                                             f"{error}, No more than 2 consecutive shots should have same focus character. "
                                                             f"Regenerate all shots by fixing this and make sure to follow all instructions."
                                                             f"{NO_INITIAL_STRING}",
                                                             MessageType.INITIAL_MESSAGE.value)
                            raise Exception(error)

                check_consecutive_characters(key_moments_response)
                if len(key_moments_response) != frame_prompt.count('Frame') - 1:
                    error = f"Missing frames, make sure to process all frames. {NO_INITIAL_STRING}"
                    moments_chat_message.add_message("user", error, MessageType.INITIAL_MESSAGE.value)
                    raise Exception(error)

                self.manage_message_history(None, moments_chat_message)
                break
            except Exception:
                import traceback
                add_message(traceback.format_exc())
                trials -= 1
                time.sleep(5 * (10 - trials))
                continue
        return self.update_frames_with_key_moment(frame_prompt, key_moments_response)

    def update_frames_with_key_moment(self, frame_prompt, key_moments_response):
        final_frame_prompt = "Must create a visual prompt for each frame only focusing on the Visual detail taking in consideration the current dialogues coming under each frame.\n"
        frame_prompt_list = frame_prompt.split("\n")
        if key_moments_response:
            for index, frame in enumerate(frame_prompt_list):
                frame_data = key_moments_response.get(str(index))
                if frame_data and "visual" in frame_data and "Frame" in frame:
                    final_frame_prompt += f"{frame}, Visual: {frame_data['visual']}\n"
                    if frame_data['focus_character']:
                        final_frame_prompt += f", hero character must be {frame_data['focus_character']}"
        return final_frame_prompt

    @retry(max_retries=3, delay=1)
    def generate_hook_prompt(self, char_library: dict, ind: int, batch_list: list, genre: str, scenes: dict,
                             word_timestamps_filename: str, retry_count=0):
        messages = ChatHistory()
        batch_dict = batch_list[ind]
        system_prompt = load_gpt_prompt("hook", genre)
        frame_prompt = self.format_scenes_message_with_dialogue(batch_dict, word_timestamps_filename, scenes)
        hook_frames = frame_prompt.split("\n")[:5]

        messages.add_message(role="system", message=system_prompt, type=MessageType.SYSTEM.value)
        character_names = list(char_library.keys())

        prompt = f"""Character list - {character_names}. Must use character name from this list.
                    Frame for which you need to generate hook prompt.
                    {hook_frames} 
                    More story segment just for content, do not generate image prompt for below story segment.
                    {batch_dict["dialogues_with_timestamps"]}\n{batch_list[ind + 1]["dialogues_with_timestamps"]}"""
        messages.add_message(role="user", message=prompt, type=MessageType.INITIAL_MESSAGE.value)

        tools = [
            {
                "type": "function",
                "function": {
                    "name": "frame_hook",
                    "description": "Hook prompt for frames",
                    "parameters": HookPrompts.model_json_schema(),
                    "required": ["prompts"]
                }
            }
        ]
        completion1 = self.gpt3prompt2_new(messages.get_message_history(), tools, "frame_hook",
                                           "frame_hook", model=LLMModels.GPT_4_POINT_1.value, retry_count=retry_count)
        tool_call1 = completion1.choices[0].message.tool_calls[0]
        arguments1 = tool_call1.function.arguments
        hook_prompts = [ele["prompt"] for ele in json.loads(arguments1)["prompts"]]

        if genre == "fantasy":
            messages.add_message(role="assistant", message=json.dumps(hook_prompts),
                                 type=MessageType.INITIAL_TOOL_CALL.value)
            messages.add_message(role="user",
                                 message="Add fantasy element to all the prompts. Decide on the setting by looking at the srt context provided. Don't change the core elements of the prompt.",
                                 type=MessageType.INITIAL_MESSAGE.value)
            completion1 = self.gpt3prompt2_new(messages.get_message_history(), tools, "frame_hook",
                                               "frame_hook", model=LLMModels.GPT_4_POINT_1.value,
                                               retry_count=retry_count)
            tool_call1 = completion1.choices[0].message.tool_calls[0]
            arguments1 = tool_call1.function.arguments
            hook_prompts = [ele["prompt"] for ele in json.loads(arguments1)["prompts"]]

        # Replace placeholders in second response
        for index, prompt in enumerate(hook_prompts):
            for name in character_names:
                placeholder = f"%{name}%"
                if placeholder in prompt:
                    hook_prompts[index] = prompt.replace(placeholder, char_library[name]["short_prompt"])
                    break

        return hook_prompts


def correct_timestamp_format(timestamp):
    try:
        datetime.datetime.strptime(timestamp, '%H:%M:%S,%f')
        return True
    except ValueError:
        try:
            datetime.datetime.strptime(timestamp, '%H:%M:%S')
            return True
        except ValueError:
            return False


def character_prompt_sanity(characters):
    for character in characters:
        if not character.get("first_name") or not character.get("gender") or not character.get(
                "body_details") or not character.get("short_prompt"):
            return False
    return True


def scene_background_prompt_sanity(scenes):
    for scene in scenes:
        if not scene.get("start_time") or not scene.get("end_time") or not scene.get(
                "scene_description") or not scene.get("scene"):
            return False
    return True


def scene_char_outfit_prompt_sanity(char_outfits):
    char_outfits_keys = ["character", "outfit_details"]
    outfit_details_v2_keys = ["colour", "type", "details", "pattern", "sleeves", "neck_line"]
    for char_outfit in char_outfits:
        if char_outfit is not None:
            if not (isinstance(char_outfit, dict) and check_keys(char_outfit, char_outfits_keys)):
                return False, (f"Error during scene_char_outfit_prompt_sanity: Missing char_outfits keys "
                               f"{get_missing_keys(char_outfit, char_outfits_keys)}")
            if not (isinstance(char_outfit, dict) and check_keys(char_outfit["outfit_details"],
                                                                 outfit_details_v2_keys)):
                return False, (f"Error during scene_char_outfit_prompt_sanity: Missing outfit_details keys "
                               f"{get_missing_keys(char_outfit['outfit_details'], outfit_details_v2_keys)}")
    return True, None


def check_keys(dictionary, keys):
    return all(key in dictionary for key in keys)


def scene_prompt_sanity(scene_prompts, batch_dict, messages, version="v2"):
    try:
        status, message = True, "Successfully validated"
        all_keys_v2 = ["start_time", "prompt", "camera_focus_point", "hero_character_info", "background"]
        hero_character_v2_keys = ["name", "face", "gaze", "speaking"]
        all_keys_v1 = ["start_time", "background", "hero_character", "hero_character_outfit",
                       "hero_character_expression",
                       "hero_character_description"]
        all_keys = all_keys_v2 if version == "v2" else all_keys_v1
        if len(scene_prompts) != len(batch_dict["image_timestamps"]):
            return False, "Error during Scene Prompt Sanity: Missing frames."
        for scene in scene_prompts:
            if not scene.get("start_time") or not correct_timestamp_format(scene.get("start_time")):
                return False, f"Error during Scene Prompt Sanity: Incorrect start time {scene.get('start_time')}"

            if not check_keys(scene, all_keys):
                return False, f"Error during Scene Prompt Sanity: Missing keys {get_missing_keys(scene, all_keys)}"

            if not scene["background"]:
                return False, "Error during Scene Prompt Sanity: Missing background details in frames."

            if version == "v2" and scene.get("hero_character_info"):
                if not check_keys(scene.get("hero_character_info"), hero_character_v2_keys):
                    return False, (f"Error during Scene Prompt Sanity: Missing hero_character_info keys "
                                   f"{get_missing_keys(scene.get('hero_character_info'), hero_character_v2_keys)}")

    except Exception as e:
        status, message = False, str(e)
    return status, message


def get_missing_keys(keys, master_keys):
    return [key for key in master_keys if key not in keys]


def get_srt_content_start_end_times(srt_content):
    subs = pysrt.from_string(srt_content)
    from utils.utils import convert_datetime_to_srt_timestamp
    start_time = convert_datetime_to_srt_timestamp(subs[0].start.to_time())
    end_time = convert_datetime_to_srt_timestamp(subs[-1].end.to_time())
    return start_time, end_time


def get_response_scenes_start_end_times(scenes):
    start_time = scenes[0]['start_time']
    end_time = scenes[-1]['end_time']
    return start_time, end_time


def check_required_fields_in_response_scenes(scenes: List[dict]):
    from utils.utils import add_message
    required_keys = {"start_time", "end_time", "scene_description", "scene"}
    if not all(required_keys.issubset(scene) for scene in scenes):
        add_message("Scenes response generated are not in correct format")
        return False
    return True


def check_scene_response_sanity(scenes: List[dict], srt_content: str):
    from utils.utils import add_message
    if not scenes:
        add_message("Scenes not generated")
        raise Exception("Scenes not generated")
    if not check_required_fields_in_response_scenes(scenes):
        return False
    try:
        srt_start_time, srt_end_time = get_srt_content_start_end_times(srt_content)
        scene_start_time, scene_end_time = get_response_scenes_start_end_times(scenes)
        if srt_start_time == scene_start_time and srt_end_time == scene_end_time:
            return True
    except Exception as e:
        add_message(f"Error parsing time in scene_background_prompt_sanity :: {e}")
        return False


def match_scenes_start_end_times_with_srt(scenes: List[dict], srt_content: str):
    from utils.utils import add_message, convert_srt_timestamp_to_datetime
    if not check_required_fields_in_response_scenes(scenes):
        raise ValueError("Missing required fields in response scenes")
    srt_start_time_str, srt_end_time_str = get_srt_content_start_end_times(srt_content)
    scene_end_time = convert_srt_timestamp_to_datetime(scenes[-1]["end_time"])
    srt_end_time = convert_srt_timestamp_to_datetime(srt_end_time_str)
    if scene_end_time == srt_end_time:
        return scenes
    elif scene_end_time > srt_end_time:
        add_message("Scene timestamp is beyond srt last end time")
    else:
        add_message("Scene timestamp is within srt last end time")
    updated_scenes = [
        scene for scene in scenes if scene_end_time < srt_end_time
    ]
    if updated_scenes:
        updated_scenes[0]["start_time"] = srt_start_time_str
        updated_scenes[-1]["end_time"] = srt_end_time_str
    return updated_scenes

