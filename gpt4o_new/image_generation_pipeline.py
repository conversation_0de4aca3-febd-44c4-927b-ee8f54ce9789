# image_generation_pipeline.py

import asyncio
import json
import os
import re
import uuid
from typing import Dict, List, Any

from openai import Async<PERSON><PERSON><PERSON><PERSON>  # <-- CHANGED: Import the async client

from gpt4o_pipeline.new_flow.services.FrameProcessor import FrameProcessor
from utils.aws_secrets import AWSSecrets
from utils.helpers import upload_to_gcs

# --- MODIFIED: Configuration & Async LLM Helper ---
# Use the AsyncOpenAI client for non-blocking API calls
client = AsyncOpenAI(api_key=AWSSecrets().get_openai_cred())

# Global shot tracking for variety
shot_history = []

def validate_prompt(prompt: str, character_names: list) -> tuple[bool, list]:
    """
    Validate generated prompt against system requirements.
    Returns (is_valid, list_of_issues)
    """
    issues = []

    # Check for character description violations
    # forbidden_descriptions = [
    #     'muscles', 'muscular', 'facial features', 'expression', 'appearance',
    #     'looks', 'outfit', 'clothing', 'physique', 'build', 'frame'
    # ]
    #
    # for desc in forbidden_descriptions:
    #     if desc in prompt.lower():
    #         issues.append(f"Contains forbidden character description: '{desc}'")

    # Check for camera angle presence
    camera_angles = [
        'low-angle', 'high-angle', 'bird\'s-eye', 'worm\'s-eye', 'dutch',
        'over-the-shoulder', 'point-of-view', 'side profile', 'back shot'
    ]

    has_camera_angle = any(angle in prompt.lower() for angle in camera_angles)
    if not has_camera_angle:
        issues.append("Missing specific camera angle")

    # Check for shot size
    shot_sizes = ['close-up', 'medium', 'wide', 'extreme close-up', 'long shot']
    has_shot_size = any(size in prompt.lower() for size in shot_sizes)
    if not has_shot_size:
        issues.append("Missing shot size specification")

    return len(issues) == 0, issues


async def call_llm(prompt: str, model: str = "gpt-4o") -> str:
    """Helper function to call the LLM API asynchronously."""
    print("--- Calling LLM for image prompt generation... ---")
    try:
        # Load the proper CINEMATIC IMAGE DIRECTOR system prompt
        with open('/Users/<USER>/PycharmProjects/discord/prompts_drama/gpt4o_image_system_prompt.txt', 'r') as f:
            system_prompt = f.read()

        # CHANGED: Use the async client and `await` the call
        response = await client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ]
        )
        content = response.choices[0].message.content
        print("--- Image Prompt Received ---")
        return content.strip()
    except Exception as e:
        print(f"An error occurred calling the LLM: {e}")
        return None


# --- UNCHANGED HELPER FUNCTIONS ---

def sanitize_filename(title: str) -> str:
    """Cleans a string to be a valid filename."""
    sanitized = re.sub(r'[^\w\s-]', '', title.lower())
    return re.sub(r'[\s-]+', '_', sanitized)


def find_context_for_line(target_line: int, chapter_structure: dict) -> (str, dict):
    """Finds the chapter title and scene object for a given SRT block number."""
    for chapter_title, scenes in chapter_structure.items():
        for scene in scenes:
            if scene['start_srt_index'] <= target_line <= scene['end_srt_index']:
                return chapter_title, scene
    return None, None


def get_dialogue_for_line(target_line: int, parsed_srt: list) -> str:
    """Finds the specific dialogue text block for a given SRT block number."""
    for block in parsed_srt:
        if block['srt_index'] == target_line:
            return block['text']
    return "No specific dialogue found for this line."


def parse_srt(srt_content: str) -> list:
    """Parses raw SRT file content into a list of structured dialogue block objects."""
    print("--- Parsing SRT content... ---")
    pattern = re.compile(
        r'(\d+)\n'
        r'(\d{2}:\d{2}:\d{2}[,.]\d{3}) --> (\d{2}:\d{2}:\d{2}[,.]\d{3})\n'
        r'([\s\S]+?)(?=\n\n|\Z)',
        re.MULTILINE
    )
    parsed_data = []
    for match in pattern.finditer(srt_content):
        block_number = int(match.group(1))
        start_time, end_time = match.group(2), match.group(3)
        timestamp = f"{start_time} --> {end_time}"
        text_content = match.group(4).strip().replace('\n', ' ')
        parsed_data.append({
            "srt_timestamp": timestamp,
            "srt_index": block_number,
            "text": text_content
        })
    print(f"--- Successfully parsed {len(parsed_data)} SRT blocks. ---")
    return parsed_data


# --- THIS IS THE MAIN CALLABLE FUNCTION FOR THE ORCHESTRATOR ---

async def process_single_srt_line_async(
        srt_block: Dict[str, Any],
        chapter_structure: Dict,
        parsed_srt: List[Dict],
        image_service: FrameProcessor,
        gcs_upload_function,  # Pass the upload function as an argument
        gcs_bucket_name: str,
        bibles_directory: str = 'story_bibles',
        genesis_bible: Dict = None,
) -> Dict[str, Any]:
    """
    Generates a prompt, an image, uploads it, and returns all data for one SRT line.
    This is the core unit of work for our parallel processing.
    """
    target_line = srt_block['srt_index']
    print(f"\n>>> Starting process for SRT block number: {target_line} <<<")

    # 1. Find context (same as before)
    chapter_title, scene_object = find_context_for_line(target_line, chapter_structure)
    if not chapter_title:
        print(f"Error: Could not find context for SRT block number {target_line}.")
        return None

    # 2. Load chapter bible
    try:
        chapter_filename = sanitize_filename(chapter_title) + '.json'
        with open(os.path.join(bibles_directory, chapter_filename), 'r') as f:
            chapter_bible = json.load(f)
    except FileNotFoundError as e:
        print(f"Error: Bible file not found for chapter '{chapter_title}'. Details: {e}")
        return None

    # 3. Assemble the prompt with character reference information
    # Extract character names from the scene for reference lock-in
    character_names = list(chapter_bible.get('character_states', {}).keys())
    character_context = f"Available characters with reference images: {', '.join(character_names)}" if character_names else "No specific characters identified in this scene."

    # Add shot variety context
    previous_shots_context = ""
    if len(shot_history) > 0:
        recent_shots = shot_history[-3:]  # Last 3 shots for context
        previous_shots_context = f"Previous shots used: {', '.join(recent_shots)}. Ensure variety in camera angle and shot type."

    final_prompt_template = f"""
    Frame dialogue: "{srt_block['text']}"
    Scene description: {scene_object['break_reason']}

    CONTEXT FOR SHOT COMPOSITION:
    - Chapter: {chapter_title}
    - Character states: {json.dumps(chapter_bible.get('character_states', {}), indent=2)}
    - Location mood: {json.dumps(chapter_bible.get('location_moods', {}), indent=2)}
    - {character_context}
    - {previous_shots_context}

    WORLD AESTHETIC: {genesis_bible.get('overarching_genre_aesthetic', 'Dystopian sci-fi')}

    Generate ONE image prompt describing this moment. Never include the dialogue text in the image prompt.
    If there's a character in the scene, mention them by exact name only (no physical descriptions).
    Focus on the most emotionally compelling visual element from the scene.
    """

    # 4. Get the final image prompt from the LLM
    image_prompt = await call_llm(final_prompt_template)
    if not image_prompt:
        print(f"Failed to generate prompt for line {target_line}. Skipping.")
        return None
    print(f"Generated Prompt for line {target_line}: '{image_prompt[:50]}...'")

    # 4.5. Validate the generated prompt
    is_valid, validation_issues = validate_prompt(image_prompt, character_names)
    if not is_valid:
        print(f"⚠️  Validation issues for line {target_line}: {validation_issues}")
        # Log issues but continue (could implement retry logic here)

    # Track shot type for variety (extract first few words that typically contain shot info)
    shot_type = ' '.join(image_prompt.split()[:4])  # First 4 words usually contain shot info
    shot_history.append(shot_type)
    if len(shot_history) > 10:  # Keep only recent history
        shot_history.pop(0)

    # 5. Generate the image and handle file paths
    # Use a temporary local file for the image
    temp_image_filename = f"temp_image_{uuid.uuid4()}.jpg"
    image_prompt_with_style = f"{image_prompt}. {open('/Users/<USER>/PycharmProjects/discord/prompts_drama/gpt4o_cinematic_style.txt').read()}"
    try:
        await image_service.generate_image(image_prompt_with_style, temp_image_filename)
        print(f"Image for line {target_line} generated at {temp_image_filename}")

        # 6. Upload to GCS
        # gcs_object_name = f"story_frames/frame_{target_line:04d}.jpg"  # Example: 'story_frames/frame_0101.jpg'
        if os.path.exists(temp_image_filename):
            image_url = await asyncio.to_thread(
                upload_to_gcs,
                local_file_path=temp_image_filename,
                destination_blob_name="story_frames/",
                # content_type can be passed if needed, e.g., 'image/jpeg'
            )
            print(f"Image for line {target_line} uploaded to {image_url}")

    except Exception as e:
        print(f"An error occurred during image generation or upload for line {target_line}: {e}")
        return None
    finally:
        # 7. Clean up the temporary local file
        if os.path.exists(temp_image_filename):
            os.remove(temp_image_filename)
            print(f"Cleaned up temporary file: {temp_image_filename}")

    # 8. Return all the necessary data for the CSV row
    return {
        "seq_number": srt_block['srt_index'],
        "Subtitle": srt_block['text'],
        "Start Time": srt_block['srt_timestamp'],
        "Prompt": image_prompt_with_style,
        "Image": image_url
    }

# NOTE: The old `if __name__ == '__main__':` block has been REMOVED.
