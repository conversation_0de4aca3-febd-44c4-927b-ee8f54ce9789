import json
import re
import os
import openai

from utils.aws_secrets import AWSSecrets

openai.api_key = AWSSecrets().get_openai_cred()


def call_llm(prompt, model="gpt-4o"):
    """Helper function to call the LLM API and get a JSON response."""
    print(f"--- Calling LLM for: {prompt[:80]}... ---")
    try:
        response = openai.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "You are a helpful assistant designed to output JSON."},
                {"role": "user", "content": prompt}
            ],
            response_format={"type": "json_object"}
        )
        content = response.choices[0].message.content
        print("--- LLM Response Received ---")
        return content
    except Exception as e:
        print(f"An error occurred calling the LLM: {e}")
        return None


# ##################################################################
# ### START OF ADDED CODE ###
# ##################################################################

def parse_srt(srt_content: str) -> list:
    """
    Parses raw SRT file content into a list of structured dialogue block objects.
    Each object contains its start/end line numbers and text content.
    """
    print("--- Parsing SRT content... ---")
    # This regex is robust and handles both comma and dot as decimal separators in timestamps.
    # It also correctly splits blocks even with extra blank lines.
    pattern = re.compile(r'(\d+)\n(\d{2}:\d{2}:\d{2}[,.]\d{3}) --> (\d{2}:\d{2}:\d{2}[,.]\d{3})\n([\s\S]+?)(?=\n\n|\Z)',
                         re.MULTILINE)

    parsed_data = []

    # We need to calculate line numbers manually as regex finds blocks, not lines.
    # We can approximate or iterate through lines, but for this context, a simple block count is sufficient
    # to demonstrate the logic. A more complex parser could be used if exact original line numbers are critical.
    # For simplicity, we'll use the SRT block number as a stand-in for the "line number".

    current_line_num = 1
    for match in pattern.finditer(srt_content):
        # The line number from the SRT file (e.g., '1', '2', '3')
        block_number = int(match.group(1))

        # The full text of the dialogue lines
        text_content = match.group(4).strip().replace('\n', ' ')

        # Calculate the number of lines this block occupies in the original file
        block_text_as_is = match.group(0)
        num_lines_in_block = len(block_text_as_is.split('\n'))

        start_line = current_line_num
        end_line = current_line_num + num_lines_in_block - 1

        parsed_data.append({
            "start_line": start_line,
            "end_line": end_line,
            "text": text_content
        })

        # Move the line counter forward, accounting for the blank line separator
        current_line_num = end_line + 2

    print(f"--- Successfully parsed {len(parsed_data)} SRT blocks. ---")
    return parsed_data


# ##################################################################
# ### END OF ADDED CODE ###
# ##################################################################


# --- Helper Functions for Bible Generation ---

def sanitize_filename(title: str) -> str:
    """Cleans a string to be a valid filename."""
    sanitized = re.sub(r'[^\w\s-]', '', title.lower())
    return re.sub(r'[\s-]+', '_', sanitized)


def get_text_for_range(start_line: int, end_line: int, parsed_srt: list) -> str:
    """Extracts the concatenated text from a list of parsed SRT blocks for a given line range."""
    text_parts = []
    for block in parsed_srt:
        if block['start_line'] <= end_line and block['end_line'] >= start_line:
            text_parts.append(block['text'])
    return " ".join(text_parts)


# --- Bible Generation Logic ---

def generate_genesis_bible(chapter_structure: dict, parsed_srt: list, output_dir: str):
    """Generates and saves the Tier 1 'Genesis Bible' from the first chapter."""
    print("\n>>> Generating Tier 1: Genesis Bible...")
    first_chapter_title = next(iter(chapter_structure))
    first_chapter_scenes = chapter_structure[first_chapter_title]
    start_line = first_chapter_scenes[0]['start_srt_index']
    end_line = first_chapter_scenes[-1]['end_srt_index']
    chapter_text = get_text_for_range(start_line, end_line, parsed_srt)

    prompt = f"""
    You are a world-building analyst. Based on the provided introductory text of a story, extract the fundamental, overarching truths of this world. Your output must be a single JSON object with keys: "core_premise", "overarching_genre_aesthetic", "fundamental_world_rules".

    Here is the introductory text (Lines {start_line}-{end_line}):
    ---
    {chapter_text}
    ---
    """
    response_json_str = call_llm(prompt)
    if not response_json_str:
        print("Failed to generate Genesis Bible.")
        return
    try:
        bible_data = json.loads(response_json_str)
        filepath = os.path.join(output_dir, 'genesis_bible.json')
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(bible_data, f, indent=2, ensure_ascii=False)
        print(f"Successfully saved Genesis Bible to {filepath}")
    except json.JSONDecodeError:
        print(f"Error: Failed to decode Genesis Bible JSON. Response was: {response_json_str}")


def generate_chapter_bible(chapter_title: str, scenes: list, parsed_srt: list, output_dir: str):
    """Generates and saves a Tier 2 'Chapter Bible' for a single chapter."""
    print(f"\n>>> Generating Tier 2 Bible for: '{chapter_title}'...")
    start_line = scenes[0]['start_srt_index']
    end_line = scenes[-1]['end_srt_index']
    chapter_text = get_text_for_range(start_line, end_line, parsed_srt)

    prompt = f"""
    You are a narrative analyst focusing on a single chapter of a story. Based on the provided chapter text, extract the key contextual information FOR THIS CHAPTER ONLY. Your output must be a single JSON object with keys: "chapter_plot_summary", "character_states", "location_moods", "key_motifs_in_chapter".

    Here is the text for '{chapter_title}' (Lines {start_line}-{end_line}):
    ---
    {chapter_text}
    ---
    """
    response_json_str = call_llm(prompt)
    if not response_json_str:
        print(f"Failed to generate bible for '{chapter_title}'.")
        return
    try:
        bible_data = json.loads(response_json_str)
        filename = sanitize_filename(chapter_title) + '.json'
        filepath = os.path.join(output_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(bible_data, f, indent=2, ensure_ascii=False)
        print(f"Successfully saved Chapter Bible to {filepath}")
    except json.JSONDecodeError:
        print(f"Error: Failed to decode Chapter Bible JSON for '{chapter_title}'. Response was: {response_json_str}")


# --- Main Orchestrator Function ---

def generate_all_bibles(chapter_structure: dict, parsed_srt: list, output_directory='bibles'):
    """Main function to pre-compute and save all Genesis and Chapter bibles."""
    print("--- Starting Bible Generation Process ---")
    os.makedirs(output_directory, exist_ok=True)
    generate_genesis_bible(chapter_structure, parsed_srt, output_directory)
    # for title, scenes in chapter_structure.items():
    #     generate_chapter_bible(title, scenes, parsed_srt, output_directory)
    print("\n--- Bible Generation Process Complete! ---")
    print(f"All bible files have been saved in the '{output_directory}' folder.")


# --- Example Usage ---
if __name__ == '__main__':
    parsed_srt_data = parse_srt(open("/Users/<USER>/PycharmProjects/discord/gpt4o_new/D_R/466386_srt_file.srt").read())
    chapter_structure_json = json.load(open("/Users/<USER>/PycharmProjects/discord/gpt4o_new/D_R/chapter_structure.json"))
    # Step 4: Run the main bible generation function.
    generate_all_bibles(
        chapter_structure=chapter_structure_json,
        parsed_srt=parsed_srt_data,
        output_directory='D_R'  # Specify a folder name
    )
