# main_orchestrator.py

import asyncio
import csv
import json
import os
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Any, Tuple
import time

from langfuse import Langfuse
from tqdm import tqdm

# Assuming these are in the same structure as before
from gpt4o_pipeline.config.settings import TaskConfig
from gpt4o_pipeline.new_flow.gpt4o_client import OpenAIWorkflowClient
from gpt4o_pipeline.new_flow.main import load_characters_from_csv
from gpt4o_pipeline.new_flow.services.FrameProcessor import FrameProcessor
from gpt4o_pipeline.new_flow.services.FramePromptService import FramePromptService
from image_generation_pipeline import (
    parse_srt,
    # We will define new workers below, old ones are not neededx
)
from llm.llm_models import LLMModels
from llm.llm_service import LLMService
from utils.aws_secrets import AWSSecrets
from utils.gpt_service import ChatHistory, MessageType
from utils.helpers import upload_to_gcs, load_gpt_prompt


# --- Configuration Helper (Unchanged) ---
def get_config(characters, srt_path: str) -> TaskConfig:
    print("Creating task config...", flush=True)
    return TaskConfig(
        task_id=str(uuid.uuid4()),
        story_id="demo_story",
        job_id="demo_job",
        language="en",
        characters_library=characters,
        srt_file_path=srt_path,
        temperature=0.7,
        image_size="1024x1024",
        image_quality="high",
        image_format="jpeg",
        image_compression=90,
        moderation_level="low",
        model="gpt-4o"
    )


def get_srt_context_with_padding(all_srt_blocks: List[Dict], start_index: int, end_index: int,
                                 padding: int = 10) -> str:
    """Gets the dialogue for a scene plus N lines before and after, in .srt format."""

    # Find the list index for the start and end SRT numbers
    start_list_idx = -1
    end_list_idx = -1
    for i, block in enumerate(all_srt_blocks):
        if block['srt_index'] == start_index:
            start_list_idx = i
        if block['srt_index'] == end_index:
            end_list_idx = i
            break  # Assume blocks are sorted

    if start_list_idx == -1 or end_list_idx == -1:
        return "Context not found."

    # Calculate padded indices
    padded_start = max(0, start_list_idx - padding)
    padded_end = min(len(all_srt_blocks), end_list_idx + 1 + padding)

    # Generate .srt format output
    context_blocks = all_srt_blocks[padded_start:padded_end]
    return "\n\n".join([
        f"{block['srt_index']}\n{block['srt_timestamp']}\n{block['text']}"
        for block in context_blocks
    ])


def process_scene_worker(
        scene_data: Dict,
        all_srt_blocks: List[Dict],
        genesis_bible: Dict,
        gcs_bucket_name: str,
        cfg: TaskConfig,
        characters: List[Dict],
        char_context_id: str,
) -> List[Dict]:
    """
    Correctly processes one scene by keeping all async operations within a single event loop
    and ensuring all blocking calls are run in a separate thread to not block the loop.
    """
    scene_number = scene_data['scene_number']
    srt_blocks_in_scene = scene_data['srt_blocks']
    print(f"\n--- [Scene {scene_number}] Starting processing... ---")

    async def main_pipeline_for_scene():
        # This function contains ALL async logic for the scene.
        client = OpenAIWorkflowClient(cfg)
        prompt_svc = FramePromptService(client._text)
        processor = FrameProcessor(prompt_svc, client._img, client._text, char_context_id, characters)

        # --- Local async helper for image generation (This part is correct) ---
        async def generate_and_upload_image(action_block: Dict, image_prompt: str, target_line) -> Dict:
            temp_image_filename = f"temp_image_{uuid.uuid4()}.jpg"
            style_prompt = f"{image_prompt}. {open('/Users/<USER>/PycharmProjects/discord/prompts_drama/gpt4o_cinematic_style.txt').read()}"
            try:
                loop = asyncio.get_running_loop()
                # This is an async call, it's fine.
                style_prompt, image_path = await loop.run_in_executor(
                    None,  # Use the default executor
                    processor.generate_image,  # The synchronous function to run
                    style_prompt,  # Arguments for the function
                    temp_image_filename,
                    action_block,
                )
                if not image_path or not os.path.exists(str(image_path)):
                    print(f"ERROR: Image generation failed for line {target_line}.")
                    return None

                # This is a blocking sync call, so it MUST be in a thread.
                image_url = await asyncio.to_thread(
                    upload_to_gcs,
                    local_file_path=str(image_path),
                    destination_blob_name="story_frames/"
                )
                print(f"Image for line {target_line} uploaded to {image_url}")

                return {
                    "seq_number": action_block['srt_index'], "Subtitle": action_block['srt_lines'], "Screenplay": action_block['text'],
                    "Start Time": action_block['srt_timestamp'], "Prompt": style_prompt,
                    "Image": image_url
                }
            finally:
                if os.path.exists(temp_image_filename):
                    os.remove(temp_image_filename)

        # --- Establish Initial Scene Context ---
        scene_start_index = srt_blocks_in_scene[0]['srt_index']
        scene_end_index = srt_blocks_in_scene[-1]['srt_index']
        full_scene_context = get_srt_context_with_padding(all_srt_blocks, scene_start_index, scene_end_index, padding=0)
        # Create the special initial prompt
        initial_context_prompt = (
            "--- GLOBAL CONTEXT: THE STORY BIBLE ---\n"
            f"{json.dumps(genesis_bible, ensure_ascii=False, indent=2)}\n\n"
            "--- SCRIPT CONTEXT FOR THE ENTIRE UPCOMING SCENE ---\n"
            f"{full_scene_context}\n\n"
            "--- YOUR TASK ---\n"
            "Acknowledge that you have read and understood this context. Your response should simply be: 'Context understood. Ready for first frame.'"
        )

        # *** FIX #1: Run the blocking 'ask' call in a thread ***
        _, previous_response_id = processor._text.ask(initial_context_prompt, previous_response_id=processor.char_upload_response_id)
        print(f"full_scene_context - {full_scene_context}")

        messages = ChatHistory()
        scene_extract_system_prompt = open("/Users/<USER>/PycharmProjects/discord/prompts_drama/scene_extract_1_improved.txt").read()
        messages.add_message(role="system",
                             message=scene_extract_system_prompt,
                             type=MessageType.SYSTEM.value)
        messages.add_message(role="user", message=f"{full_scene_context}", type=MessageType.INITIAL_MESSAGE.value)

        start_time = time.time()
        scene_extract_1 = LLMService("gemini").generate_response(messages, LLMModels.GEMINI_TWO_POINT_5_FLASH.value,
                                                               1,
                                                               llm_call_type="SUCCESS",
                                                               call_identifier="scene_extract_1")
        extract_1_time = time.time() - start_time
        print(f"scene_extract_1 took {extract_1_time:.2f} seconds - {scene_extract_1}")

        messages = ChatHistory()
        scene_extract_system_prompt = open("/Users/<USER>/PycharmProjects/discord/prompts_drama/scene_extract_2_improved.txt").read()
        messages.add_message(role="system",
                             message=scene_extract_system_prompt,
                             type=MessageType.SYSTEM.value)
        scene_extract_1 = scene_extract_1.replace("'''json", "").replace("'''", "")
        messages.add_message(role="user", message=f"{scene_extract_1}", type=MessageType.INITIAL_MESSAGE.value)

        start_time = time.time()
        scene_extract = LLMService("gemini").generate_response(messages, LLMModels.GEMINI_TWO_POINT_5_FLASH.value,
                                                               1,
                                                               llm_call_type="SUCCESS",
                                                               call_identifier="scene_extract_2")
        extract_2_time = time.time() - start_time
        print(f"scene_extract_2 took {extract_2_time:.2f} seconds - {scene_extract}")

        # srt_blocks = [
        #     {
        #         'srt_index': sub.index,
        #         'text': sub.content,
        #         'srt_timestamp': f"{str(sub.start).replace('.', ',')[:12].zfill(12)} --> {str(sub.end).replace('.', ',')[:12].zfill(12)}"
        #     }
        #     for sub in srt.parse(scene_extract)
        # ]


        image_tasks = []
        # scene_extract = open("/Users/<USER>/Downloads/scene_extract.json").read()
        scene_extract = json.loads(scene_extract.replace("```json", "").replace("```", ""))

        # Map the scene_extract blocks to actual SRT indices from the scene
        scene_start_srt_index = srt_blocks_in_scene[0]['srt_index']

        for i, action_block in enumerate(scene_extract):
            # Calculate the actual SRT index for this action block
            actual_srt_index = scene_start_srt_index + i
            action_block["srt_index"] = actual_srt_index

            # Also add the original SRT timestamp if we can find it
            matching_srt_block = next((block for block in srt_blocks_in_scene if block['srt_index'] == actual_srt_index), None)
            if matching_srt_block:
                action_block["srt_timestamp"] = matching_srt_block['srt_timestamp']
                action_block["srt_lines"] = matching_srt_block['text']
            else:
                # Fallback if we can't find exact match
                action_block["srt_timestamp"] = f"Unknown timestamp for index {actual_srt_index}"
                action_block["srt_lines"] = action_block.get('text', '')

            try:
                target_line = actual_srt_index

                prompt, new_response_id = await asyncio.to_thread(
                    processor.build_prompt,
                    frame_dialogue=action_block,
                    previous_response_id=previous_response_id
                )
                previous_response_id = new_response_id

                if not prompt: continue

                # This part is correct: create a concurrent task for image generation
                task = asyncio.create_task(generate_and_upload_image(action_block, prompt, i))
                image_tasks.append(task)
            except Exception as e:
                import traceback
                traceback.print_exc()
                print(f"--- ERROR in prompt generation loop for SRT {target_line}: {e} ---")
                continue

        # --- Collect results as they complete (This part is correct) ---
        scene_results = []
        for future in tqdm(asyncio.as_completed(image_tasks), total=len(image_tasks),
                           desc=f"Images for Scene {scene_number}"):
            result = await future
            if result:
                scene_results.append(result)
        return scene_results

    return asyncio.run(main_pipeline_for_scene())

# --- REFACTORED: MAIN ORCHESTRATION FUNCTION ---
async def run_scene_based_pipeline(
        srt_path: str,
        characters_csv_path: str,
        chapter_structure_path: str,
        bibles_directory: str,
        output_csv_path: str,
        gcs_bucket: str,
        max_scene_workers: int = 4
):
    try:
        # 1. Load all initial data (once)
        print("--- Loading initial data ---")
        with open(srt_path, "r") as f:
            srt_content = f.read()
        with open(chapter_structure_path, "r") as f:
            chapter_structure = json.load(f)
        with open(os.path.join(bibles_directory, 'genesis_bible.json'), 'r') as f:
            genesis_bible = json.load(f)
        all_srt_blocks = parse_srt(srt_content)
        characters = load_characters_from_csv(characters_csv_path)
        cfg = get_config(characters, srt_path)

        # 2. *** KEY CHANGE: Prepare story context ONCE at the beginning ***
        print("--- Preparing story context (once for all scenes)... ---")
        client = OpenAIWorkflowClient(cfg)
        context = await client.prepare_story_context(srt_content, characters)
        char_context_id = context.get("character_context_id")
        print("--- Story context prepared successfully. ---")

        # 3. Create a flat list of scene tasks
        scene_tasks = []
        for chapter, scenes in chapter_structure.items():
            for scene in scenes:
                if scene['scene_number'] and scene['scene_number'] in only_scenes:  # Your filter
                # if scene['scene_number']:
                    start, end = scene['start_srt_index'], scene['end_srt_index']
                    scene_srt_blocks = [block for block in all_srt_blocks if start <= block['srt_index'] <= end]
                    if scene_srt_blocks:
                        scene_tasks.append({"scene_number": scene['scene_number'], "srt_blocks": scene_srt_blocks})

        if not scene_tasks: print("No scenes to process. Exiting."); return

        # 4. Use ThreadPoolExecutor to run scene workers in parallel
        print(f"\n--- Starting parallel processing with up to {max_scene_workers} scene workers ---")
        all_results = []
        with ThreadPoolExecutor(max_workers=max_scene_workers, thread_name_prefix='Main_Scene_Worker') as executor:
            future_to_scene = {
                executor.submit(
                    process_scene_worker, task, all_srt_blocks, genesis_bible, gcs_bucket,
                    cfg, characters, char_context_id  # <-- Pass the ID, not the raw data
                ): task['scene_number'] for task in scene_tasks
            }
            # ... Result handling loop is unchanged ...
            for future in tqdm(as_completed(future_to_scene), total=len(scene_tasks), desc="Processing Scenes"):
                scene_number = future_to_scene[future]
                try:
                    scene_result_list = future.result()
                    if scene_result_list: all_results.extend(scene_result_list)
                except Exception as exc:
                    print(f"--- ERROR: Scene {scene_number} generated an exception: {exc} ---")
                    import traceback;
                    traceback.print_exc()

        # 5. Sort and write to CSV (unchanged)
        all_results.sort(key=lambda x: x['seq_number'])
        print(f"\n--- Writing {len(all_results)} total results to {output_csv_path} ---")
        if not all_results: print("No successful results to write."); return
        with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ["seq_number", "Subtitle", "Screenplay","Start Time", "Prompt", "Image"]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(all_results)
        print("--- Pipeline finished successfully! ---")

    except Exception as e:
        import traceback;
        traceback.print_exc()


# --- REFACTORED: MAIN ASYNC SETUP AND KICK-OFF ---
async def run_flow():
    SRT_FILE_PATH = "/Users/<USER>/Downloads/439381_srt_file.srt"
    CHARACTERS_CSV = "/Users/<USER>/Downloads/char_canvas.csv"
    CHAPTER_STRUCTURE_FILE = "gpt4o_new/chapter_structure.json"

    STORY_BIBLES_DIR = "gpt4o_new/story_bibles"
    os.makedirs(STORY_BIBLES_DIR, exist_ok=True)

    OUTPUT_CSV = "gpt4o_new/output_story_frames.csv"
    GCS_BUCKET_NAME = "your-gcs-bucket-name"  # <-- IMPORTANT: CHANGE THIS

    # --- Async setup phase (runs once) ---
    characters = load_characters_from_csv(CHARACTERS_CSV)
    cfg = get_config(characters, SRT_FILE_PATH)

    # We must run the pipeline in a separate thread because the main thread is running an async loop
    # The pipeline itself is synchronous (using threads), so we can't await it directly.
    # We use a dedicated function for the blocking call.
    def run_pipeline_synchronously():
        # This setup needs to be inside the sync function because aiohttp.ClientSession
        # cannot be shared across different event loops/threads easily.
        async def setup_and_run():
            client = OpenAIWorkflowClient(cfg)
            context = await client.prepare_story_context(open(SRT_FILE_PATH).read(), characters)
            prompt_svc = FramePromptService(client._text)
            img_svc = client._img
            processor = FrameProcessor(prompt_svc, img_svc, client._text, context.get("character_context_id"),
                                       characters)

            # --- Run the blocking, threaded pipeline ---
            run_scene_based_pipeline(
                srt_path=SRT_FILE_PATH,
                chapter_structure_path=CHAPTER_STRUCTURE_FILE,
                bibles_directory=STORY_BIBLES_DIR,
                output_csv_path=OUTPUT_CSV,
                gcs_bucket=GCS_BUCKET_NAME,
                frame_processor=processor,
                max_scene_workers=4  # e.g., process 4 scenes at once
            )

        # Each thread needs its own event loop to run the async setup
        asyncio.run(setup_and_run())

    # Run the entire synchronous pipeline in a separate thread to not block the main event loop
    await asyncio.to_thread(run_pipeline_synchronously)


if __name__ == '__main__':
    global only_scenes
    only_scenes = [1]

    langfuse = Langfuse(
        public_key="pk-lf-7b2afbd1-3ddf-4f9f-a023-3eb3eb3e194e",
        secret_key="sk-lf-842c9e7e-9ccb-467d-9cc7-aa81fedad42c",
        host="http://localhost:3000", tracing_enabled=False
    )
    os.environ["OPENAI_API_KEY"] = AWSSecrets().get_openai_cred()

    # Default
    # SRT_FILE_PATH = "/Users/<USER>/Downloads/439381_srt_file.srt"
    # CHARACTERS_CSV = "/Users/<USER>/Downloads/char_canvas.csv"
    # CHAPTER_STRUCTURE_FILE = "chapter_structure.json"
    # STORY_BIBLES_DIR = "story_bibles"

    SRT_FILE_PATH = "/Users/<USER>/PycharmProjects/discord/gpt4o_new/D_R/466386_srt_file.srt"
    CHARACTERS_CSV = "/Users/<USER>/PycharmProjects/discord/gpt4o_new/D_R/char_canvas.csv"
    CHAPTER_STRUCTURE_FILE = "/Users/<USER>/PycharmProjects/discord/gpt4o_new/D_R/chapter_structure.json"
    STORY_BIBLES_DIR = "D_R"

    OUTPUT_CSV = "output_story_frames.csv"
    GCS_BUCKET_NAME = "your-gcs-bucket-name"

    # We now call the async orchestrator directly.
    asyncio.run(run_scene_based_pipeline(
        srt_path=SRT_FILE_PATH,
        characters_csv_path=CHARACTERS_CSV,
        chapter_structure_path=CHAPTER_STRUCTURE_FILE,
        bibles_directory=STORY_BIBLES_DIR,
        output_csv_path=OUTPUT_CSV,
        gcs_bucket=GCS_BUCKET_NAME,
        max_scene_workers=40
    ))