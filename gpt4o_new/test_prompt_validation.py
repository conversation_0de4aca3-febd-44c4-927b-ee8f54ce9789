#!/usr/bin/env python3
"""
Test script to validate the improved image prompt generation system.
This script analyzes the existing CSV output and tests the new validation functions.
"""

import csv
import json
import os

def validate_prompt(prompt: str, character_names: list) -> tuple:
    """
    Validate generated prompt against system requirements.
    Returns (is_valid, list_of_issues)
    """
    issues = []

    # Check for character description violations
    forbidden_descriptions = [
        'muscles', 'muscular', 'facial features', 'expression', 'appearance',
        'looks', 'outfit', 'clothing', 'physique', 'build', 'frame', 'fur', 'tail',
        'grip', 'bulging', 'ripple', 'powerful', 'aura of strength'
    ]

    for desc in forbidden_descriptions:
        if desc in prompt.lower():
            issues.append(f"Contains forbidden character description: '{desc}'")

    # Check for camera angle presence
    camera_angles = [
        'low-angle', 'high-angle', 'bird\'s-eye', 'worm\'s-eye', 'dutch',
        'over-the-shoulder', 'point-of-view', 'side profile', 'back shot'
    ]

    has_camera_angle = any(angle in prompt.lower() for angle in camera_angles)
    if not has_camera_angle:
        issues.append("Missing specific camera angle")

    # Check for shot size
    shot_sizes = ['close-up', 'medium', 'wide', 'extreme close-up', 'long shot']
    has_shot_size = any(size in prompt.lower() for size in shot_sizes)
    if not has_shot_size:
        issues.append("Missing shot size specification")

    return len(issues) == 0, issues

def analyze_existing_csv():
    """Analyze the existing CSV output for compliance issues."""
    print("=== ANALYZING EXISTING CSV OUTPUT ===\n")
    
    # Load character names from story bibles
    character_names = []
    bibles_dir = 'story_bibles'
    
    for filename in os.listdir(bibles_dir):
        if filename.endswith('.json') and filename != 'genesis_bible.json':
            with open(os.path.join(bibles_dir, filename), 'r') as f:
                bible = json.load(f)
                if 'character_states' in bible:
                    character_names.extend(bible['character_states'].keys())
    
    character_names = list(set(character_names))  # Remove duplicates
    print(f"Characters with reference images: {character_names}\n")
    
    # Analyze CSV
    issues_found = []
    total_prompts = 0
    
    with open('output_story_frames.csv', 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if row['Prompt']:  # Skip empty rows
                total_prompts += 1
                seq_num = row['seq_number']
                prompt = row['Prompt']
                
                is_valid, validation_issues = validate_prompt(prompt, character_names)
                
                if not is_valid:
                    issues_found.append({
                        'seq_number': seq_num,
                        'prompt_preview': prompt[:100] + '...',
                        'issues': validation_issues
                    })
    
    print(f"ANALYSIS RESULTS:")
    print(f"Total prompts analyzed: {total_prompts}")
    print(f"Prompts with issues: {len(issues_found)}")
    print(f"Success rate: {((total_prompts - len(issues_found)) / total_prompts * 100):.1f}%\n")
    
    if issues_found:
        print("DETAILED ISSUES FOUND:")
        print("-" * 80)
        for issue in issues_found[:10]:  # Show first 10 issues
            print(f"Sequence {issue['seq_number']}:")
            print(f"  Prompt: {issue['prompt_preview']}")
            print(f"  Issues: {', '.join(issue['issues'])}")
            print()
    
    return issues_found, total_prompts

def test_specific_prompts():
    """Test specific problematic prompts from the CSV."""
    print("\n=== TESTING SPECIFIC PROBLEMATIC PROMPTS ===\n")
    
    # Example problematic prompts from the CSV
    test_cases = [
        {
            'seq': 421,
            'prompt': "Low-angle medium shot, Damian stands confidently on an urban rooftop, his silhouette striking against the moonlit sky. His muscles ripple with power as he exudes an aura of strength, the golden tones of his fur and tail subtly highlighted.",
            'expected_issues': ['Contains forbidden character description: muscles']
        },
        {
            'seq': 422,
            'prompt': "High-angle close-up, Damian's hand effortlessly bends a steel pipe, his powerful grip and bulging muscles illuminated by stark moonlight",
            'expected_issues': ['Contains forbidden character description: muscles']
        }
    ]
    
    character_names = ['Damian', 'Gary', 'Amy']
    
    for test_case in test_cases:
        print(f"Testing sequence {test_case['seq']}:")
        print(f"Prompt: {test_case['prompt'][:100]}...")
        
        is_valid, issues = validate_prompt(test_case['prompt'], character_names)
        
        print(f"Valid: {is_valid}")
        print(f"Issues found: {issues}")
        print(f"Expected issues: {test_case['expected_issues']}")
        
        # Check if we caught the expected issues
        caught_expected = any(expected in str(issues) for expected in test_case['expected_issues'])
        print(f"✅ Validation working correctly: {caught_expected}")
        print("-" * 50)

def generate_improvement_report():
    """Generate a comprehensive improvement report."""
    print("\n=== IMPROVEMENT RECOMMENDATIONS ===\n")
    
    recommendations = [
        {
            'priority': 'HIGH',
            'issue': 'Wrong system prompt being used',
            'solution': 'Updated image_generation_pipeline.py to use the proper CINEMATIC IMAGE DIRECTOR prompt',
            'status': '✅ FIXED'
        },
        {
            'priority': 'HIGH', 
            'issue': 'Character description violations',
            'solution': 'Added validation function to detect forbidden character descriptions',
            'status': '✅ IMPLEMENTED'
        },
        {
            'priority': 'MEDIUM',
            'issue': 'Lack of shot variety tracking',
            'solution': 'Added shot_history tracking and context in prompts',
            'status': '✅ IMPLEMENTED'
        },
        {
            'priority': 'MEDIUM',
            'issue': 'Missing camera angle specifications',
            'solution': 'Validation function now checks for camera angles and shot sizes',
            'status': '✅ IMPLEMENTED'
        },
        {
            'priority': 'LOW',
            'issue': 'Generic user prompt template',
            'solution': 'Enhanced prompt template with better context structure',
            'status': '✅ IMPROVED'
        }
    ]
    
    for rec in recommendations:
        print(f"[{rec['priority']}] {rec['issue']}")
        print(f"   Solution: {rec['solution']}")
        print(f"   Status: {rec['status']}")
        print()

if __name__ == "__main__":
    print("🎬 IMAGE PROMPT VALIDATION TEST SUITE")
    print("=" * 50)
    
    # Run analysis
    issues, total = analyze_existing_csv()
    
    # Test specific cases
    test_specific_prompts()
    
    # Generate report
    generate_improvement_report()
    
    print("\n🎯 NEXT STEPS:")
    print("1. Run the updated pipeline on a small test batch")
    print("2. Compare new results with existing CSV")
    print("3. Verify character reference integration is working")
    print("4. Check shot variety improvement")
    print("5. Monitor validation warnings during generation")
