from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class TaskConfig:
    task_id: str
    story_id: str
    job_id: str
    language: str
    characters_library: Dict[str, Any]
    srt_file_path: str
    temperature: float = 1.0
    image_size: str = "1024x1024"
    image_quality: str = "high"
    image_format: str = "jpeg"
    image_compression: int = 100
    moderation_level: str = "low"
    model: str = "gpt-4o"
    system_prompt = open("/Users/<USER>/PycharmProjects/discord/prompts_drama/gpt4o_image_system_prompt.txt").read()
    nsfw_rephrase_system_prompt = open("/Users/<USER>/PycharmProjects/discord/prompts_drama/gpt4o_nsfw_rephrase.txt").read()
