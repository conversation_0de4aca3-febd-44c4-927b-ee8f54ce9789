import asyncio
import base64
import os
from typing import Any, List, Dict

import requests
from langfuse import observe


class ImageService:
    def __init__(self, session, cfg):
        print("Initializing ImageService", flush=True)
        self._s = session
        self._model = cfg.model
        self.size = cfg.image_size
        self.quality = cfg.image_quality
        self.fmt = cfg.image_format
        self.comp = cfg.image_compression
        self.mod_lvl = cfg.moderation_level
        self._system_prompt = cfg.system_prompt
        self._nsfw_rephrase_system_prompt = cfg.nsfw_rephrase_system_prompt
        self._downloaded: set[str] = set()

    @observe
    def generate(self, image_prompt: str, outfile: str, *, nsfw=False, previous_response_id=None) -> bool:
        print(f"Generating image to {outfile}", flush=True)
        try:
            rsp = self._s.responses.create(
                model=self._model,
                previous_response_id=previous_response_id,
                instructions=self._nsfw_rephrase_system_prompt if nsfw else self._system_prompt,
                input=[{"role": "user", "content": [{"type": "input_text", "text": image_prompt}]}],
                tools=[{
                    "type": "image_generation",
                    "size": self.size,
                    "quality": self.quality,
                    "output_format": self.fmt,
                    "output_compression": self.comp,
                    "moderation": self.mod_lvl,
                }],
                tool_choice={"type": "image_generation"},
            )

            call = next((o for o in rsp.output or [] if o.type == "image_generation_call"), None)
            if not call or not call.result:
                print(f"Image generation failed for {outfile}: No result in API response.", flush=True)
                return False

            with open(outfile, "wb") as f:
                f.write(base64.b64decode(call.result))
            return True
        except Exception as e:

            print(f"Image generation for {outfile} threw an exception: {e}", flush=True)
            return False

    @observe
    def fallback_generate(self, prompt: str, outfile: str, ref_imgs: Dict[str, Dict], action_block: dict) -> bool:
        print("Falling back to image generation via edit/generate API", flush=True)
        fp_list: List[Any] = []

        try:
            for name in action_block["characters_present"]:
                if name not in ref_imgs:
                    # Try splitting full name and search for first name, then last name
                    name_parts = name.split()
                    if len(name_parts) > 1:
                        first_name = name_parts[0]
                        last_name = name_parts[-1]

                        if first_name in ref_imgs:
                            name = first_name
                        elif last_name in ref_imgs:
                            name = last_name
                        else:
                            continue
                    else:
                        continue

                if name not in ref_imgs:
                    continue

                loc = f"ref_{name.lower().replace(' ', '_')}.jpg"
                if not os.path.exists(loc):
                    resp = requests.get(ref_imgs[name]["image_url"])
                    if resp.status_code == 200:
                        with open(loc, "wb") as f:
                            f.write(resp.content)
                    else:
                        continue
                self._downloaded.add(loc)
                fp_list.append(open(loc, "rb"))

            if fp_list:
                image_data_list = [fp.read() for fp in fp_list]
                image_tuples_for_api = [
                    (os.path.basename(fp.name), data)
                    for fp, data in zip(fp_list, image_data_list)
                ]
                rsp = self._s.images.edit(
                    model="gpt-image-1",
                    image=image_tuples_for_api,
                    prompt=prompt,
                    size=self.size,
                )
            else:
                rsp = self._s.images.generate(
                    model="gpt-image-1",
                    prompt=prompt,
                    n=1,
                    size=self.size,
                )

            with open(outfile, "wb") as f:
                f.write(base64.b64decode(rsp.data[0].b64_json))

            return True

        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"Fallback image generation threw an exception: {e}", flush=True)
            return False

        finally:
            for fp in fp_list:
                try:
                    fp.close()
                except Exception:
                    pass

    async def cleanup(self):
        for p in self._downloaded:
            try:
                await asyncio.to_thread(os.remove, p)
            except FileNotFoundError:
                pass
        self._downloaded.clear()