#!/usr/bin/env python3
"""
Test script for the improved replace_pronoun_in_srt function using the real SRT file
"""

import os
import sys
import shutil
import re
from datetime import datetime

# Add the project root to Python path
sys.path.append('/Users/<USER>/PycharmProjects/discord')

try:
    from utils.gpt_service import OpenAIService
    from utils.helpers import load_gpt_prompt
    from utils.utils import parse_srt  # Use the existing SRT parser
    from gpt4o_new.image_generation_pipeline import parse_srt as parse_srt_alt  # Alternative parser
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the project directory")
    sys.exit(1)

def count_pronouns_in_text(text):
    """Count pronouns in text"""
    pronouns = ['he', 'she', 'him', 'her', 'his', 'hers', 'they', 'them', 'their', 'theirs']
    text_lower = text.lower()
    count = 0
    for pronoun in pronouns:
        # Use word boundaries to avoid partial matches
        pattern = r'\b' + re.escape(pronoun) + r'\b'
        count += len(re.findall(pattern, text_lower))
    return count

def analyze_srt_content(filename):
    """Analyze the SRT file content using existing parsers"""
    print(f"📊 Analyzing SRT file: {filename}")
    print("=" * 60)

    try:
        # Use the existing parse_srt function from utils
        entries = parse_srt(filename)  # Returns list of (index, timecode, text) tuples
        total_entries = len(entries)
        total_pronouns = 0

        print(f"📈 Total SRT entries: {total_entries}")

        # Count pronouns
        for index, timecode, text in entries:
            total_pronouns += count_pronouns_in_text(text)

        print(f"👤 Total pronouns found: {total_pronouns}")

        # Show first few entries with pronouns
        print(f"\n📝 First 5 entries with pronouns:")
        pronoun_count = 0
        for i, (index, timecode, text) in enumerate(entries[:20]):  # Check first 20 entries
            if count_pronouns_in_text(text) > 0:
                pronoun_count += 1
                print(f"  Entry {index}: {text[:80]}...")
                if pronoun_count >= 5:
                    break

        return {
            'total_entries': total_entries,
            'total_pronouns': total_pronouns,
            'entries': entries
        }
    except Exception as e:
        print(f"❌ Failed to analyze file: {str(e)}")
        return None

def test_pronoun_replacement_real_file():
    """Test the improved pronoun replacement function with real SRT file"""
    print("🧪 Testing replace_pronoun_in_srt with real SRT file")
    print("=" * 60)
    
    # File paths
    original_file = "test_srt_file.srt"
    backup_file = "test_srt_file_backup.srt"
    
    if not os.path.exists(original_file):
        print(f"❌ Test file not found: {original_file}")
        return
    
    # Create backup
    shutil.copy2(original_file, backup_file)
    print(f"💾 Created backup: {backup_file}")
    
    try:
        # Analyze original file
        print(f"\n🔍 ORIGINAL FILE ANALYSIS:")
        original_analysis = analyze_srt_content(original_file)
        
        if not original_analysis:
            print("❌ Failed to analyze original file")
            return
        
        # Load system prompt for fantasy genre (since this appears to be a fantasy story)
        print(f"\n📋 Loading system prompt...")
        system_prompt = load_gpt_prompt("replace_pronoun", "fantasy")
        print(f"✅ System prompt loaded ({len(system_prompt)} characters)")
        
        # Create OpenAI service instance
        service = OpenAIService(task_id="test_real_srt", story_id="test_story_real")
        
        # Run the pronoun replacement
        print(f"\n🔄 Running pronoun replacement...")
        start_time = datetime.now()
        
        try:
            service.replace_pronoun_in_srt(original_file, system_prompt)
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            print(f"✅ Processing completed in {processing_time:.2f} seconds")
            
        except Exception as e:
            print(f"❌ Processing failed: {str(e)}")
            # Restore backup
            shutil.copy2(backup_file, original_file)
            print(f"🔄 Restored original file from backup")
            return
        
        # Analyze processed file
        print(f"\n🔍 PROCESSED FILE ANALYSIS:")
        processed_analysis = analyze_srt_content(original_file)
        
        if not processed_analysis:
            print("❌ Failed to analyze processed file")
            return
        
        # Simple comparison using our parse function
        print(f"\n📊 COMPARISON RESULTS:")
        try:
            original_entries = parse_srt(backup_file)
            processed_entries = parse_srt(original_file)

            if len(original_entries) == len(processed_entries):
                print("✅ Structure validation: PASSED")
                print(f"📊 Entry count: {len(original_entries)}")

                # Check for timestamp preservation
                timestamp_issues = 0
                for i, ((orig_idx, orig_time, orig_text), (proc_idx, proc_time, proc_text)) in enumerate(zip(original_entries, processed_entries)):
                    if orig_time != proc_time or orig_idx != proc_idx:
                        timestamp_issues += 1
                        if timestamp_issues <= 3:  # Show first 3 issues
                            print(f"⚠️  Entry {i+1}: Timestamp/index mismatch")

                if timestamp_issues == 0:
                    print("✅ All timestamps and indices preserved")
                else:
                    print(f"❌ {timestamp_issues} timestamp/index issues found")
            else:
                print("❌ Structure validation: FAILED")
                print(f"🚨 Entry count mismatch: {len(original_entries)} vs {len(processed_entries)}")
        except Exception as e:
            print(f"❌ Comparison failed: {str(e)}")
        
        # Summary
        print(f"\n📋 SUMMARY:")
        print(f"  Original entries: {original_analysis['total_entries']}")
        print(f"  Processed entries: {processed_analysis['total_entries']}")
        print(f"  Original pronouns: {original_analysis['total_pronouns']}")
        print(f"  Processed pronouns: {processed_analysis['total_pronouns']}")
        
        if original_analysis['total_entries'] == processed_analysis['total_entries']:
            print(f"  ✅ Entry count preserved")
        else:
            print(f"  ❌ Entry count mismatch!")
        
        if processed_analysis['total_pronouns'] < original_analysis['total_pronouns']:
            replaced_pronouns = original_analysis['total_pronouns'] - processed_analysis['total_pronouns']
            print(f"  🔄 Pronouns replaced: {replaced_pronouns}")
        else:
            print(f"  ⚠️  Pronoun count didn't decrease as expected")
        
        print(f"\n📁 Files:")
        print(f"  Original (backup): {backup_file}")
        print(f"  Processed: {original_file}")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # Restore backup if something went wrong
        if os.path.exists(backup_file):
            shutil.copy2(backup_file, original_file)
            print(f"🔄 Restored original file from backup")

if __name__ == "__main__":
    test_pronoun_replacement_real_file()
